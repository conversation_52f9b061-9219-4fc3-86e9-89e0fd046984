//pls 测试开始block: 19196734

use std::collections::HashMap;

use alloy::{primitives::{Address, B256}, providers::Provider, rpc::types::Filter, sol_types::SolEvent};

use crate::{connector::Connector, vira::{dex::{factory::{DexFactory, FACTORY}, uni_v2::factory::IUniswapV2Factory, uni_v3::factory::IUniswapV3Factory}, errors::DEXError}};

pub enum DiscoverableFactory {
    UniV2Factory,
    UniV3Factory,
}

impl DiscoverableFactory {
    pub fn discovery_event_signature(&self) -> B256 {
        match self {
            DiscoverableFactory::UniV2Factory => IUniswapV2Factory::PairCreated::SIGNATURE_HASH,
            DiscoverableFactory::UniV3Factory => IUniswapV3Factory::PoolCreated::SIGNATURE_HASH,
        }
    }
}

pub async fn discover_factories(
    factories: Vec<DiscoverableFactory>,
    connector: &Connector,
    from_block: u64,
    step: u64,
) -> Result<Vec<FACTORY>, DEXError> {
    let mut event_signatures = vec![];

    for factory in factories {
        event_signatures.push(factory.discovery_event_signature());
    }
    let mut from_block = from_block;
    let block_filter = Filter::new().event_signature(event_signatures);
    let current_block = connector.provider().get_block_number().await?;

    // Set up filter and events to filter each block you are searching by
    let mut identified_factories: HashMap<Address, (FACTORY, u64)> = HashMap::new();

    while from_block < current_block {
        // Get pair created event logs within the block range
        let mut target_block = from_block + step - 1;
        if target_block > current_block {
            target_block = current_block;
        }
        let block_filter = block_filter.clone();
        let logs = connector.provider().get_logs(&block_filter.from_block(from_block).to_block(target_block)).await?;
        //println!("found {} logs in block range {} - {}", logs.len(), from_block, target_block);
        for log in logs {
            if let Some((_, amms_length)) = identified_factories.get_mut(&log.address()) {
                *amms_length += 1;
            } else {
                let mut factory = FACTORY::try_from(log.topics()[0]).unwrap();

                match &mut factory {
                    FACTORY::UniV2Factory(uniswap_v2_factory) => {
                        let config = uniswap_v2_factory.data_mut();
                        config.addr = log.address();
                        config.creation_block = log.block_number.ok_or(DEXError::BlockNumberNotFound)?;
                        println!("find V2 factory at block {:?}, addr: {:?}", config.creation_block, log.address());
                    }
                    FACTORY::UniV3Factory(uniswap_v3_factory) => {
                        let config = uniswap_v3_factory.data_mut();
                        config.addr = log.address();
                        config.creation_block = log.block_number.ok_or(DEXError::BlockNumberNotFound)?;
                        println!("find V3 factory at block {:?}, addr: {:?}", config.creation_block, log.address());
                    }
                }

                identified_factories.insert(log.address(), (factory, 0));
            }
        }

        from_block += step;
    }
    let mut filtered_factories = vec![];
    //tracing::trace!(number_of_amms_threshold, "checking threshold");
    for (address, (factory, amms_length)) in identified_factories {
        filtered_factories.push(factory);
        println!("factory {:?} has {:?} AMMs => adding", address, amms_length);
    }

    Ok(filtered_factories)
}

#[cfg(test)]
mod tests {
    use super::*;
    use alloy::providers::Provider;

    use crate::{config, connector::Connector};

    #[tokio::test]
    async fn test_discover_factories() {
        println!("test_discover_factories");
        let config = config::pls::new();
        let connector = Connector::new(&config.server).await;
        let latest_block = connector.provider().get_block_number().await.unwrap();
        println!("Latest block number: {latest_block}");
        discover_factories(vec![DiscoverableFactory::UniV2Factory, DiscoverableFactory::UniV3Factory], &connector, 19196734, 1000).await.unwrap();
    }
}