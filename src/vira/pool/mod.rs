use alloy::primitives::utils::format_units;
use alloy::primitives::{Address, U256};
use alloy::rpc::types::Log;
use serde::{Serialize, Deserialize};

/*
#[derive(Debug, Clone)]
pub enum PairVersion {
    //V1 = 1, //uniswapV1
    //V2 = 2, //uniswapV2
    //V3 = 3, //uniswapV3
    //V2OnlyRouter = 21, //只能通过v2 router swap        //(oec)jf
    //V2Stable = 22,      //uniswapV2 stable x3y+y3x //(op)velo
    //V2StableFee = 23,  //uniswapV2 stable x3y+y3x and check pair individual fee //(zk)mute
    //V2Eoa = 24,   //只能使用eoa账户交易的routerv2
    Bera = 100, //beradex, 走独立的router
}
*/



#[derive(Debug, <PERSON><PERSON>, Default, PartialEq, Eq, Serialize, Deserialize)]
pub enum SwapWay {
    #[default] Pool,
    Router,
    RouterByEoa
}

#[derive(Debug, <PERSON><PERSON>, Default, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Status {
    #[default] UnChecked = 0,
    Good = 1,
    Fee = 2,
    Bad = 3,
    LowValue = 4,
    OddGood = 5, //fee奇怪但是可以交易
    OddBad = 6,  //通缩了交易不了
    NoStable = 7, //没有关联的稳定币
    Unknown = 8, //未知原因
}

impl std::fmt::Display for Status {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self)
    }
}


#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PoolData {
    pub addr : Address,

    //pub factory : String,
    pub routers : Vec<Address>,

    pub ver : u16, //pair的版本号, 对应合约的ver
    pub swap_way : SwapWay, //pair的交易途径: 默认使用pool的swap

    pub tokens : Vec<PoolDataToken>,

    pub fp : U256, //uniswapV2 factory fee or uniswapV3pool fee
    pub status : Status, //0)未检查fee，1)正常fee的pool 2)交易异常，可能不能卖出，或高于20%fee，或可能token太少，或没有关联的稳定币
    pub update_time : u64, //单位秒

    pub stable : bool, //基于uniswapV2修改的stable pool
}


#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PoolDataToken {
    pub addr : Address,
    pub index : usize,
    pub symbol : String,
    pub decimal : u8,
    pub reserve : U256,
    pub fee : U256,      //token_in fee

    pub weight : U256,  //balancer
}


pub trait DexPool {
    //data 
    fn data(&self) -> &PoolData;
    fn data_mut(&mut self) -> &mut PoolData;

    fn addr(&self) -> Address {
        self.data().addr
    }

    // Updates the AMM data from a log.
    fn sync(&mut self, log: &Log) -> Result<bool, EventLogError> {
        Ok(false)
    }

    fn reserve(&self, token0 : &Address, token1 : &Address) -> (U256, U256) {
        let mut reserve0 = U256::ZERO;
        let mut reserve1 = U256::ZERO;
        self.data().tokens.iter().for_each(|t| {
            if t.addr == *token0 {
                reserve0 = t.reserve;
            } else if t.addr == *token1 {
                reserve1 = t.reserve;
            }
        });
        (reserve0, reserve1)
    }

    fn reserve_by_index(&self, index0 : usize, index1 : usize) -> (U256, U256) {
        let tokens = &self.data().tokens;
        let token0 = &tokens[index0];
        let token1 = &tokens[index1];

        (token0.reserve, token1.reserve)
    }

    //找出pairs里价值最低的pair，然后所有pair一起消费
    //ratio: 1/10000
    fn consume(&mut self, numerator:&U256, denominator:&U256) -> Vec<U256>{
        let p = denominator - numerator;
        let data = self.data_mut();
        if p.is_zero() {
            data.tokens.iter_mut().for_each(|t| t.reserve = U256::ZERO);
        } else {
            data.tokens.iter_mut().for_each(|t| t.reserve = t.reserve * p / denominator);
        }
        data.tokens.iter().map(|d| { d.reserve }).collect()
    }

    fn simulate_swap(&self, base_token: Address, quote_token: Address, amount_in: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {
        Ok(U256::ZERO)
    }

    fn simulate_swap_by_index(&self, in_index : usize, out_index : usize, amount_in: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {
        Ok(U256::ZERO)
    }

    fn update_reserve(&mut self, token_in: Address, token_out: Address, amount_in: U256, amount_out: U256) {}

    fn update_reserve_by_index(&mut self, in_index: usize, out_index: usize, amount_in: U256, amount_out: U256) {}

}


macro_rules! pool {
    ($($pool_type:ident),+ $(,)?) => {
        #[derive(Debug, Clone, Serialize, Deserialize)]
        pub enum POOL {
            $($pool_type($pool_type),)+
        }

        impl DexPool for POOL {
            fn data(&self) -> &PoolData {
                match self {
                    $(POOL::$pool_type(pool) => pool.data(),)+
                }
            }

            fn data_mut(&mut self) -> &mut PoolData {
                match self {
                    $(POOL::$pool_type(pool) => pool.data_mut(),)+
                }
            }

            fn simulate_swap(&self, token_in: Address, token_out: Address, amount: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {
                match self {
                    $(POOL::$pool_type(pool) => pool.simulate_swap(token_in, token_out, amount, custom_fee),)+
                }
            }

            fn simulate_swap_by_index(&self, in_index : usize, out_index : usize, amount: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {
                match self {
                    $(POOL::$pool_type(pool) => pool.simulate_swap_by_index(in_index, out_index, amount, custom_fee),)+
                }
            }

            fn update_reserve(&mut self, token_in: Address, token_out: Address, amount_in: U256, amount_out: U256) {
                match self {
                    $(POOL::$pool_type(pool) => pool.update_reserve(token_in, token_out, amount_in, amount_out),)+
                }
            }

            fn update_reserve_by_index(&mut self, in_index: usize, out_index: usize, amount_in: U256, amount_out: U256) {
                match self {
                    $(POOL::$pool_type(pool) => pool.update_reserve_by_index(in_index, out_index, amount_in, amount_out),)+
                }
            }


            fn sync(&mut self, log: &Log) -> Result<bool, EventLogError> {
                match self {
                    $(POOL::$pool_type(pool) => pool.sync(log),)+
                }
            }

        }
    };
}


use std::fmt;
use chrono::{DateTime, Utc};
impl fmt::Display for POOL {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let data = self.data();
        write!(f, "(POOL) {}\n", data.addr)?;
        for (_, token) in data.tokens.iter().enumerate() {
            write!(f, "    |-- [{}] {}, d{}, r:{}\n", token.symbol, token.addr, token.decimal, format_units(token.reserve, token.decimal).unwrap())?;
            if !token.fee.is_zero() {
                write!(f, "    |   fee: {},\n", token.fee)?;
            }
            if !token.weight.is_zero() {
                write!(f, "    |   weight: {},\n", token.weight)?;
            }
        }
        write!(f, "    |-- fp: {}, ver: {}, st: {}\n", data.fp, data.ver, data.status)?;
        write!(f,     "    `-- {} (UTC-8),\n", DateTime::<Utc>::from_timestamp(data.update_time as i64, 0).unwrap().format("%Y-%m-%d %H:%M:%S"))?;
        write!(f, "\n")
    }
}

impl std::fmt::Display for PoolData {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "PoolData: {}\n", self.addr)?;
        for token in self.tokens.iter() {
            write!(f, "    |-- [{}] {}, d{}, r:{}\n", token.symbol, token.addr, token.decimal, format_units(token.reserve, token.decimal).unwrap())?;
            if !token.fee.is_zero() {
                write!(f, "    |   fee: {},\n", token.fee)?;
            }
            if !token.weight.is_zero() {
                write!(f, "    |   weight: {},\n", token.weight)?;
            }
        }
        write!(f, "    |-- fp: {}, ver: {}, st: {}\n", self.fp, self.ver, self.status)?;
        write!(f, "    `-- {} (UTC-8),\n", DateTime::<Utc>::from_timestamp(self.update_time as i64, 0).unwrap().format("%Y-%m-%d %H:%M:%S"))?;
        write!(f, "\n")
    }
}


use super::dex::uni_v2::pool::UniV2Pool;
use super::dex::uni_v3::pool::UniV3Pool;
use super::errors::{EventLogError, SwapSimulationError};
//pool!(BeraPool, UniV1Pool, UniV2Pool);
pool!(UniV2Pool, UniV3Pool);

// 添加一个宏来为DexPool类型实现Display特性
#[macro_export]
macro_rules! impl_pool_display {
    ($($pool_type:ty),+ $(,)?) => {
        $(
            impl std::fmt::Display for $pool_type {
                fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                    let data = self.data();
                    let type_name = stringify!($pool_type).split("::").last().unwrap_or(stringify!($pool_type));
                    write!(f, "({}) {}\n", type_name, data.addr)?;
                    for token in data.tokens.iter() {
                        write!(f, "    |-- [{}] {}, d{}, r:{}\n", 
                            token.symbol, token.addr, token.decimal, 
                            alloy::primitives::utils::format_units(token.reserve, token.decimal).unwrap())?;
                        if !token.fee.is_zero() {
                            write!(f, "    |   fee: {},\n", token.fee)?;
                        }
                        if !token.weight.is_zero() {
                            write!(f, "    |   weight: {},\n", token.weight)?;
                        }
                    }
                    write!(f, "    |-- fp: {}, ver: {}, st: {}\n", data.fp, data.ver, data.status)?;
                    write!(f, "    `-- {} (UTC-8),\n", 
                        chrono::DateTime::<chrono::Utc>::from_timestamp(data.update_time as i64, 0)
                            .unwrap().format("%Y-%m-%d %H:%M:%S"))?;
                    write!(f, "\n")
                }
            }
        )+
    };
}