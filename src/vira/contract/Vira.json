{"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "_delegate", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}], "name": "addOpts", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}], "internalType": "struct FeedList[]", "name": "feedList", "type": "tuple[]"}], "name": "checkFeed", "outputs": [{"components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}], "internalType": "struct FeedList[]", "name": "", "type": "tuple[]"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}], "internalType": "struct FeedList[]", "name": "feedList", "type": "tuple[]"}, {"internalType": "address", "name": "eth", "type": "address"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "name": "feed", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAdapter", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "getBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "logicContact", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "removeOperators", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "addr", "type": "address"}], "name": "setAdapter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_new", "type": "address"}], "name": "setLogicContact", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawAllEthEx", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "withdrawAllEx", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "withdrawAllToEx", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawToEx", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": {"object": "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", "sourceMap": "322:4087:5:-:0;;;569:116;;;;;;;;;-1:-1:-1;591:10:5;;1269:95:31;;1350:1;1322:31;;-1:-1:-1;;;1322:31:31;;;;;;;;:::i;:::-;;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;613:26:5::1;:10;628;613:14;:26::i;:::-;649:29;:10;672:4;649:14;:29::i;:::-;322:4087:::0;;2912:187:31;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:31;;;-1:-1:-1;;;;;;3020:17:31;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;4040:175:1:-;4117:18;4121:4;4127:7;4117:3;:18::i;:::-;4116:19;4108:63;;;;-1:-1:-1;;;4108:63:1;;;;;;;:::i;:::-;-1:-1:-1;;;;;4181:20:1;:11;:20;;;;;;;;;;;:27;;-1:-1:-1;;4181:27:1;4204:4;4181:27;;;4040:175::o;4407:200::-;4479:4;-1:-1:-1;;;;;4503:21:1;;4495:68;;;;-1:-1:-1;;;4495:68:1;;;;;;;:::i;:::-;-1:-1:-1;;;;;;4580:20:1;;:11;:20;;;;;;;;;;;;;4407:200;;;;;:::o;139:96:41:-;176:7;-1:-1:-1;;;;;73:54:41;;205:24;7:126;241:118;328:24;346:5;328:24;:::i;:::-;323:3;316:37;241:118;;:::o;365:222::-;496:2;481:18;;509:71;485:9;553:6;509:71;:::i;1327:419::-;1531:2;1544:47;;;1516:18;;1608:131;1516:18;1182:2;699:19;;908:33;751:4;742:14;;885:57;1303:12;;;955:366;2351:419;2555:2;2568:47;;;2540:18;;2632:131;2540:18;2206:2;699:19;;1892:34;751:4;742:14;;1869:58;-1:-1:-1;;;1944:15:41;;;1937:29;2327:12;;;1979:366;2351:419;322:4087:5;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "322:4087:5:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4387:12;;4377:23;;-1:-1:-1;;;;;4387:12:5;4377:9;:23::i;:::-;322:4087;841:211;;;;;;;;;;-1:-1:-1;841:211:5;;;;;:::i;:::-;;:::i;1422:138::-;;;;;;;;;;-1:-1:-1;1422:138:5;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1566:133;;;;;;;;;;-1:-1:-1;1566:133:5;;;;;:::i;:::-;;:::i;1170:113::-;;;;;;;;;;-1:-1:-1;1170:113:5;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2293:101:31:-;;;;;;;;;;;;;:::i;1705:148:5:-;;;;;;;;;;-1:-1:-1;1705:148:5;;;;;:::i;:::-;;:::i;2211:123::-;;;:::i;1638:85:31:-;;;;;;;;;;-1:-1:-1;1684:7:31;1710:6;-1:-1:-1;;;;;1710:6:31;1638:85;;;;;;;:::i;1859:346:5:-;;;;;;;;;;-1:-1:-1;1859:346:5;;;;;:::i;:::-;;:::i;2340:94::-;;;;;;;;;;-1:-1:-1;2340:94:5;;;;;:::i;:::-;;:::i;3265:488::-;;;;;;:::i;:::-;;:::i;2726:533::-;;;;;;;;;;-1:-1:-1;2726:533:5;;;;;:::i;:::-;;:::i;:::-;;;;;;;;:::i;421:27::-;;;;;;;;;;-1:-1:-1;421:27:5;;;;-1:-1:-1;;;;;421:27:5;;;2569:101;;;;;;;;;;-1:-1:-1;2569:101:5;;;;;:::i;:::-;2622:7;2648:15;;;:8;:15;;;;;;-1:-1:-1;;;;;2648:15:5;;2569:101;2459:104;;;;;;;;;;-1:-1:-1;2459:104:5;;;;;:::i;:::-;;:::i;1058:106::-;;;;;;;;;;-1:-1:-1;1058:106:5;;;;;:::i;:::-;;:::i;3806:492::-;;;;;;:::i;:::-;;:::i;2543:215:31:-;;;;;;;;;;-1:-1:-1;2543:215:31;;;;;:::i;:::-;;:::i;1289:127:5:-;;;;;;;;;;-1:-1:-1;1289:127:5;;;;;:::i;:::-;;:::i;3806:492::-;749:26;:10;764;749:14;:26::i;:::-;741:76;;;;-1:-1:-1;;;741:76:5;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;;;3895:28:5;::::1;3887:60;;;;-1:-1:-1::0;;;3887:60:5::1;;;;;;;:::i;:::-;3999:14;3996:1;3993::::0;3980:34:::1;4099:1;4096::::0;4080:14:::1;4077:1;4061:14;4054:5;4041:60;4135:16;4132:1;4129::::0;4114:38:::1;4173:6:::0;4192:38;;::::1;;4263:16;4260:1;4253:27;4192:38;4211:16;4208:1;4201:27;4166:116;;3966:326;3806:492:::0;:::o;841:211::-;1531:13:31;:11;:13::i;:::-;920:9:5::1;915:131;939:9;:16;935:1;:20;915:131;;;981:24;992:9;1002:1;992:12;;;;;;;;:::i;:::-;;;;;;;981:10;:24::i;:::-;976:59;;1007:28;1022:9;1032:1;1022:12;;;;;;;;:::i;:::-;;;;;;;1007:10;:14;;:28;;;;:::i;:::-;957:3;::::0;::::1;:::i;:::-;;;915:131;;;;841:211:::0;:::o;1422:138::-;1522:31;;-1:-1:-1;;;1522:31:5;;1496:7;;-1:-1:-1;;;;;1522:23:5;;;;;:31;;1546:6;;1522:31;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1515:38;;1422:138;;;;;:::o;1566:133::-;1531:13:31;:11;:13::i;:::-;1658:34:5::1;::::0;-1:-1:-1;;;1658:34:5;;-1:-1:-1;;;;;1658:22:5;::::1;::::0;::::1;::::0;:34:::1;::::0;1681:2;;1685:6;;1658:34:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;1566:133:::0;;;:::o;1170:113::-;1229:4;1252:24;:10;1267:8;1252:14;:24::i;2293:101:31:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;1705:148:5:-;1531:13:31;:11;:13::i;:::-;1807:38:5::1;::::0;-1:-1:-1;;;1807:38:5;;-1:-1:-1;;;;;1772:22:5;::::1;::::0;::::1;::::0;1795:10:::1;::::0;1772:22;;1807:23:::1;::::0;:38:::1;::::0;1839:4:::1;::::0;1807:38:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1772:74;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;2211:123::-:0;1531:13:31;:11;:13::i;:::-;2276:51:5::1;::::0;2284:10:::1;::::0;2305:21:::1;2276:51:::0;::::1;;;::::0;::::1;::::0;;;2305:21;2284:10;2276:51;::::1;;;;;;;;;;;;;::::0;::::1;;;;;;2211:123::o:0;1859:346::-;1531:13:31;:11;:13::i;:::-;1955:9:5::1;1950:249;1970:6;:13;1966:1;:17;1950:249;;;2004:12;2026:6;2033:1;2026:9;;;;;;;;:::i;:::-;;;;;;;2004:32;;2050:15;2068:5;-1:-1:-1::0;;;;;2068:15:5::1;;2092:4;2068:30;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2050:48:::0;-1:-1:-1;2116:11:5;;2112:77:::1;;2147:27;::::0;-1:-1:-1;;;2147:27:5;;-1:-1:-1;;;;;2147:14:5;::::1;::::0;::::1;::::0;:27:::1;::::0;2162:2;;2166:7;;2147:27:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;2112:77;1990:209;;1985:3;;;;;:::i;:::-;;;;1950:249;;2340:94:::0;1531:13:31;:11;:13::i;:::-;2408:12:5::1;:19:::0;;-1:-1:-1;;;;;;2408:19:5::1;-1:-1:-1::0;;;;;2408:19:5;;;::::1;::::0;;;::::1;::::0;;2340:94::o;3265:488::-;749:26;:10;764;749:14;:26::i;:::-;741:76;;;;-1:-1:-1;;;741:76:5;;;;;;;:::i;:::-;3377:15:::1;3395;3406:3;3395:10;:15::i;:::-;3377:33;;3438:5;3428:7;:15;3420:46;;;;-1:-1:-1::0;;;3420:46:5::1;;;;;;;:::i;:::-;3476:27;::::0;-1:-1:-1;;;3476:27:5;;-1:-1:-1;;;;;3476:20:5;::::1;::::0;::::1;::::0;:27:::1;::::0;3497:5;;3476:27:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;::::0;::::1;;;;;;;;;3518:9;3513:234;3529:19:::0;;::::1;3513:234;;;3615:9;3629:8;;3638:1;3629:11;;;;;;;:::i;:::-;:16;::::0;::::1;:11;::::0;;::::1;;:16:::0;;::::1;::::0;-1:-1:-1;3629:16:5::1;:::i;:::-;-1:-1:-1::0;;;;;3629:21:5::1;3658:8;;3667:1;3658:11;;;;;;;:::i;:::-;;;;;;:22;;;3629:56;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3614:71;;;3707:4;3699:37;;;;-1:-1:-1::0;;;3699:37:5::1;;;;;;;:::i;:::-;-1:-1:-1::0;3550:3:5;::::1;::::0;::::1;:::i;:::-;;;;3513:234;;;;3367:386;3265:488:::0;;;;:::o;2726:533::-;2794:17;2813:7;;;2859:360;2879:8;:15;2875:1;:19;2859:360;;;2915:15;2933:8;2942:1;2933:11;;;;;;;;:::i;:::-;;;;;;;:16;;;-1:-1:-1;;;;;2933:24:5;;2915:42;;2993:7;2971:8;2980:1;2971:11;;;;;;;;:::i;:::-;;;;;;;:19;;:29;;;;;3028:8;3037:1;3028:11;;;;;;;;:::i;:::-;;;;;;;:15;;;3018:7;:25;3014:195;;;3063:14;3098:7;3080:8;3089:1;3080:11;;;;;;;;:::i;:::-;;;;;;;:15;;;:25;;;;:::i;:::-;3063:42;-1:-1:-1;3131:14:5;3140:5;3063:42;3131:14;:::i;:::-;3123:22;;3188:6;3163:8;3172:1;3163:11;;;;;;;;:::i;:::-;;;;;;;:22;;:31;;;;;3045:164;3014:195;-1:-1:-1;2896:3:5;;;;:::i;:::-;;;;2859:360;;;-1:-1:-1;3236:8:5;;2726:533;-1:-1:-1;;2726:533:5:o;2459:104::-;1531:13:31;:11;:13::i;:::-;2534:15:5::1;::::0;;;:8:::1;:15;::::0;;;;;:22;;-1:-1:-1;;;;;;2534:22:5::1;-1:-1:-1::0;;;;;2534:22:5;;::::1;::::0;;;::::1;::::0;;2459:104::o;1058:106::-;1531:13:31;:11;:13::i;:::-;1130:27:5::1;:10;1148:8:::0;1130:17:::1;:27::i;2543:215:31:-:0;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:31;::::1;2623:91;;2700:1;2672:31;;-1:-1:-1::0;;;2672:31:31::1;;;;;;;;:::i;2623:91::-;2723:28;2742:8;2723:18;:28::i;1289:127:5:-:0;1371:38;;-1:-1:-1;;;1371:38:5;;1345:7;;-1:-1:-1;;;;;1371:23:5;;;;;:38;;1403:4;;1371:38;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;4407:200:1:-;4479:4;-1:-1:-1;;;;;4503:21:1;;4495:68;;;;-1:-1:-1;;;4495:68:1;;;;;;;:::i;:::-;-1:-1:-1;;;;;;4580:20:1;:11;:20;;;;;;;;;;;;;;;4407:200::o;1796:162:31:-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:31;735:10:32;1855:23:31;1851:101;;735:10:32;1901:40:31;;-1:-1:-1;;;1901:40:31;;;;;;;;:::i;4040:175:1:-;4117:18;4121:4;4127:7;4117:3;:18::i;:::-;4116:19;4108:63;;;;-1:-1:-1;;;4108:63:1;;;;;;;:::i;:::-;-1:-1:-1;;;;;4181:20:1;:11;:20;;;;;;;;;;;:27;;-1:-1:-1;;4181:27:1;4204:4;4181:27;;;4040:175::o;2912:187:31:-;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:31;;;-1:-1:-1;;;;;;3020:17:31;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;4221:180:1:-;4300:18;4304:4;4310:7;4300:3;:18::i;:::-;4292:64;;;;-1:-1:-1;;;4292:64:1;;;;;;;:::i;:::-;-1:-1:-1;;;;;4366:20:1;4389:5;4366:20;;;;;;;;;;;:28;;-1:-1:-1;;4366:28:1;;;4221:180::o;565::41:-;-1:-1:-1;;;610:1:41;603:88;710:4;707:1;700:15;734:4;731:1;724:15;751:281;-1:-1:-1;;549:2:41;529:14;;525:28;826:6;822:40;964:6;952:10;949:22;928:18;916:10;913:34;910:62;907:88;;;975:18;;:::i;:::-;1011:2;1004:22;-1:-1:-1;;751:281:41:o;1038:129::-;1072:6;1099:20;73:2;67:9;;7:75;1099:20;1089:30;;1128:33;1156:4;1148:6;1128:33;:::i;:::-;1038:129;;;:::o;1173:311::-;1250:4;1340:18;1332:6;1329:30;1326:56;;;1362:18;;:::i;:::-;-1:-1:-1;1412:4:41;1400:17;;;1462:15;;1173:311::o;1745:96::-;1782:7;-1:-1:-1;;;;;1679:54:41;;1811:24;1613:126;1847:122;1920:24;1938:5;1920:24;:::i;:::-;1913:5;1910:35;1900:63;;1959:1;1956;1949:12;1975:139;2046:20;;2075:33;2046:20;2075:33;:::i;2137:710::-;2233:5;2258:81;2274:64;2331:6;2274:64;:::i;:::-;2258:81;:::i;:::-;2374:21;;;2249:90;-1:-1:-1;2422:4:41;2411:16;;;;2463:17;;2451:30;;2493:15;;;2490:122;;;2523:79;197:1;194;187:12;2523:79;2638:6;2621:220;2655:6;2650:3;2647:15;2621:220;;;2730:3;2759:37;2792:3;2780:10;2759:37;:::i;:::-;2747:50;;-1:-1:-1;2826:4:41;2817:14;;;;2672;2621:220;;;2625:21;2239:608;;2137:710;;;;;:::o;2870:370::-;2941:5;2990:3;2983:4;2975:6;2971:17;2967:27;2957:122;;2998:79;197:1;194;187:12;2998:79;3115:6;3102:20;3140:94;3230:3;3222:6;3215:4;3207:6;3203:17;3140:94;:::i;:::-;3131:103;2870:370;-1:-1:-1;;;;2870:370:41:o;3246:539::-;3330:6;3379:2;3367:9;3358:7;3354:23;3350:32;3347:119;;;3385:79;197:1;194;187:12;3385:79;3505:31;;3563:18;3552:30;;3549:117;;;3585:79;197:1;194;187:12;3585:79;3690:78;3760:7;3751:6;3740:9;3736:22;3690:78;:::i;3791:474::-;3859:6;3867;3916:2;3904:9;3895:7;3891:23;3887:32;3884:119;;;3922:79;197:1;194;187:12;3922:79;4042:1;4067:53;4112:7;4092:9;4067:53;:::i;:::-;4057:63;;4013:117;4169:2;4195:53;4240:7;4231:6;4220:9;4216:22;4195:53;:::i;:::-;4185:63;;4140:118;3791:474;;;;;:::o;4354:118::-;4459:5;4441:24;4436:3;4429:37;4354:118;;:::o;4478:222::-;4609:2;4594:18;;4622:71;4598:9;4666:6;4622:71;:::i;4706:122::-;4797:5;4779:24;4271:77;4834:139;4905:20;;4934:33;4905:20;4934:33;:::i;4979:619::-;5056:6;5064;5072;5121:2;5109:9;5100:7;5096:23;5092:32;5089:119;;;5127:79;197:1;194;187:12;5127:79;5247:1;5272:53;5317:7;5297:9;5272:53;:::i;:::-;5262:63;;5218:117;5374:2;5400:53;5445:7;5436:6;5425:9;5421:22;5400:53;:::i;:::-;5390:63;;5345:118;5502:2;5528:53;5573:7;5564:6;5553:9;5549:22;5528:53;:::i;:::-;5518:63;;5473:118;4979:619;;;;;:::o;5604:329::-;5663:6;5712:2;5700:9;5691:7;5687:23;5683:32;5680:119;;;5718:79;197:1;194;187:12;5718:79;5838:1;5863:53;5908:7;5888:9;5863:53;:::i;6035:109::-;6009:13;;6002:21;6116;5939:90;6150:210;6275:2;6260:18;;6288:65;6264:9;6326:6;6288:65;:::i;6366:118::-;6453:24;6471:5;6453:24;:::i;6490:222::-;6621:2;6606:18;;6634:71;6610:9;6678:6;6634:71;:::i;6718:684::-;6811:6;6819;6868:2;6856:9;6847:7;6843:23;6839:32;6836:119;;;6874:79;197:1;194;187:12;6874:79;6994:31;;7052:18;7041:30;;7038:117;;;7074:79;197:1;194;187:12;7074:79;7179:78;7249:7;7240:6;7229:9;7225:22;7179:78;:::i;7556:596::-;7657:8;7667:6;7717:3;7710:4;7702:6;7698:17;7694:27;7684:122;;7725:79;197:1;194;187:12;7725:79;-1:-1:-1;7825:20:41;;7868:18;7857:30;;7854:117;;;7890:79;197:1;194;187:12;7890:79;8004:4;7996:6;7992:17;7980:29;;8058:3;8050:4;8042:6;8038:17;8028:8;8024:32;8021:41;8018:128;;;8065:79;197:1;194;187:12;8065:79;7556:596;;;;;:::o;8158:905::-;8290:6;8298;8306;8314;8363:2;8351:9;8342:7;8338:23;8334:32;8331:119;;;8369:79;197:1;194;187:12;8369:79;8489:31;;8547:18;8536:30;;8533:117;;;8569:79;197:1;194;187:12;8569:79;8682:108;8782:7;8773:6;8762:9;8758:22;8682:108;:::i;:::-;8664:126;;;;8460:340;8839:2;8865:53;8910:7;8901:6;8890:9;8886:22;8865:53;:::i;:::-;8855:63;;8810:118;8967:2;8993:53;9038:7;9029:6;9018:9;9014:22;8993:53;:::i;:::-;8983:63;;8938:118;8158:905;;;;;;;:::o;9681:1066::-;9756:5;9800:4;9788:9;9783:3;9779:19;9775:30;9772:117;;;9808:79;197:1;194;187:12;9808:79;9907:21;9923:4;9907:21;:::i;:::-;9898:30;-1:-1:-1;9987:1:41;10027:49;10072:3;10052:9;10027:49;:::i;:::-;10002:75;;-1:-1:-1;10146:2:41;10187:49;10232:3;10208:22;;;10187:49;:::i;:::-;10180:4;10173:5;10169:16;10162:75;10098:150;10306:2;10347:49;10392:3;10383:6;10372:9;10368:22;10347:49;:::i;:::-;10340:4;10333:5;10329:16;10322:75;10258:150;10470:2;10511:49;10556:3;10547:6;10536:9;10532:22;10511:49;:::i;:::-;10504:4;10497:5;10493:16;10486:75;10418:154;10637:3;10679:49;10724:3;10715:6;10704:9;10700:22;10679:49;:::i;:::-;10672:4;10665:5;10661:16;10654:75;10582:158;9681:1066;;;;:::o;10778:788::-;10900:5;10925:107;10941:90;11024:6;10941:90;:::i;10925:107::-;11067:21;;;10916:116;-1:-1:-1;11115:4:41;11104:16;;11168:4;11156:17;;11144:30;;11186:15;;;11183:122;;;11216:79;197:1;194;187:12;11216:79;11331:6;11314:246;11348:6;11343:3;11340:15;11314:246;;;11423:3;11452:63;11511:3;11499:10;11452:63;:::i;:::-;11440:76;;-1:-1:-1;11545:4:41;11536:14;;;;11374:4;11365:14;11314:246;;11597:422;11694:5;11743:3;11736:4;11728:6;11724:17;11720:27;11710:122;;11751:79;197:1;194;187:12;11751:79;11868:6;11855:20;11893:120;12009:3;12001:6;11994:4;11986:6;11982:17;11893:120;:::i;12025:591::-;12135:6;12184:2;12172:9;12163:7;12159:23;12155:32;12152:119;;;12190:79;197:1;194;187:12;12190:79;12310:31;;12368:18;12357:30;;12354:117;;;12390:79;197:1;194;187:12;12390:79;12495:104;12591:7;12582:6;12571:9;12567:22;12495:104;:::i;13418:1028::-;13627:23;;13557:4;13548:14;;;13663:63;13552:3;13627:23;13663:63;:::i;:::-;13572:164;13817:4;13810:5;13806:16;13800:23;13836:63;13893:4;13888:3;13884:14;13870:12;13836:63;:::i;:::-;13746:163;13990:4;13983:5;13979:16;13973:23;14009:63;14066:4;14061:3;14057:14;14043:12;14009:63;:::i;:::-;13919:163;14167:4;14160:5;14156:16;14150:23;14186:63;14243:4;14238:3;14234:14;14220:12;14186:63;:::i;:::-;14092:167;14347:4;14340:5;14336:16;14330:23;14366:63;14423:4;14418:3;14414:14;14400:12;14366:63;:::i;14452:283::-;14573:10;14594:98;14688:3;14680:6;14594:98;:::i;:::-;-1:-1:-1;;14724:4:41;14715:14;;14452:283::o;14932:940::-;15103:3;15132:80;15206:5;12743:12;;12622:140;15132:80;12915:19;;;12967:4;12958:14;;;;13121;;;15500:1;15485:362;15510:6;15507:1;15504:13;15485:362;;;15586:6;15580:13;15613:115;15724:3;15709:13;15613:115;:::i;:::-;15606:122;-1:-1:-1;14869:4:41;14860:14;;15741:96;-1:-1:-1;;15532:1:41;15525:9;15485:362;;;-1:-1:-1;15863:3:41;;14932:940;-1:-1:-1;;;;;14932:940:41:o;15878:587::-;16139:2;16152:47;;;16124:18;;16216:160;16124:18;16362:6;16216:160;:::i;:::-;16208:168;;16386:72;16454:2;16443:9;16439:18;16430:6;16386:72;:::i;:::-;15878:587;;;;;:::o;16471:329::-;16530:6;16579:2;16567:9;16558:7;16554:23;16550:32;16547:119;;;16585:79;197:1;194;187:12;16585:79;16705:1;16730:53;16775:7;16755:9;16730:53;:::i;16806:474::-;16874:6;16882;16931:2;16919:9;16910:7;16906:23;16902:32;16899:119;;;16937:79;197:1;194;187:12;16937:79;17057:1;17082:53;17127:7;17107:9;17082:53;:::i;17691:366::-;17918:2;12915:19;;17833:3;12967:4;12958:14;;17601:34;17578:58;;-1:-1:-1;;;17665:2:41;17653:15;;17646:32;17847:74;-1:-1:-1;17930:93:41;-1:-1:-1;18048:2:41;18039:12;;17691:366::o;18063:419::-;18267:2;18280:47;;;18252:18;;18344:131;18252:18;18344:131;:::i;18663:366::-;18890:2;12915:19;;18805:3;12967:4;12958:14;;-1:-1:-1;;;18605:45:41;;18819:74;-1:-1:-1;18902:93:41;-1:-1:-1;19020:2:41;19011:12;;18663:366::o;19035:419::-;19239:2;19252:47;;;19224:18;;19316:131;19224:18;19316:131;:::i;19460:180::-;-1:-1:-1;;;19505:1:41;19498:88;19605:4;19602:1;19595:15;19629:4;19626:1;19619:15;19646:180;-1:-1:-1;;;19691:1:41;19684:88;19791:4;19788:1;19781:15;19815:4;19812:1;19805:15;19832:233;19871:3;-1:-1:-1;;19933:5:41;19930:77;19927:103;;20010:18;;:::i;:::-;-1:-1:-1;20057:1:41;20046:13;;19832:233::o;20071:143::-;20153:13;;20175:33;20153:13;20175:33;:::i;20220:351::-;20290:6;20339:2;20327:9;20318:7;20314:23;20310:32;20307:119;;;20345:79;197:1;194;187:12;20345:79;20465:1;20490:64;20546:7;20526:9;20490:64;:::i;20577:332::-;20736:2;20721:18;;20749:71;20725:9;20793:6;20749:71;:::i;:::-;20830:72;20898:2;20887:9;20883:18;20874:6;20830:72;:::i;20915:116::-;6009:13;;6002:21;20985;5939:90;21037:137;21116:13;;21138:30;21116:13;21138:30;:::i;21180:345::-;21247:6;21296:2;21284:9;21275:7;21271:23;21267:32;21264:119;;;21302:79;197:1;194;187:12;21302:79;21422:1;21447:61;21500:7;21480:9;21447:61;:::i;21705:366::-;21932:2;12915:19;;21847:3;12967:4;12958:14;;-1:-1:-1;;;21648:44:41;;21861:74;-1:-1:-1;21944:93:41;21531:168;22077:419;22281:2;22294:47;;;22266:18;;22358:131;22266:18;22358:131;:::i;23179:379::-;23363:3;23528;23385:147;22775:398;23740:366;23967:2;12915:19;;23882:3;12967:4;12958:14;;-1:-1:-1;;;23681:46:41;;23896:74;-1:-1:-1;23979:93:41;23564:170;24112:419;24316:2;24329:47;;;24301:18;;24393:131;24301:18;24393:131;:::i;24537:194::-;24668:9;;;24690:11;;;24687:37;;;24704:18;;:::i;24737:191::-;24866:9;;;24888:10;;;24885:36;;;24901:18;;:::i;25161:366::-;25388:2;12915:19;;25303:3;12967:4;12958:14;;25074:34;25051:58;;-1:-1:-1;;;25138:2:41;25126:15;;25119:29;25317:74;-1:-1:-1;25400:93:41;24934:221;25533:419;25737:2;25750:47;;;25722:18;;25814:131;25722:18;25814:131;:::i;26145:366::-;26372:2;12915:19;;26287:3;12967:4;12958:14;;26098:33;26075:57;;26301:74;-1:-1:-1;26384:93:41;25958:181;26517:419;26721:2;26734:47;;;26706:18;;26798:131;26706:18;26798:131;:::i;27168:366::-;27395:2;12915:19;;27310:3;12967:4;12958:14;;27082:34;27059:58;;-1:-1:-1;;;27146:2:41;27134:15;;27127:28;27324:74;-1:-1:-1;27407:93:41;26942:220;27540:419;27744:2;27757:47;;;27729:18;;27821:131;27729:18;27821:131;:::i", "linkReferences": {}}, "methodIdentifiers": {"_delegate(address)": "f13101e9", "addOpts(address[])": "110c6dbf", "checkFeed((address,uint256,uint256,uint256,uint256)[])": "c8de451d", "feed((address,uint256,uint256,uint256,uint256)[],address,uint256)": "aa239e1d", "getAdapter(uint256)": "ddeadbb6", "getBalance(address)": "f8b2cb4f", "getBalanceOf(address,address)": "53290b44", "isOperator(address)": "6d70f7ae", "logicContact()": "d45fe99c", "owner()": "8da5cb5b", "removeOperators(address)": "e5486bc3", "renounceOwnership()": "715018a6", "setAdapter(uint256,address)": "e26bbe80", "setLogicContact(address)": "a1d448ab", "transferOwnership(address)": "f2fde38b", "withdrawAllEthEx()": "8642fde2", "withdrawAllEx(address)": "7793c048", "withdrawAllToEx(address[],address)": "9b7395e1", "withdrawToEx(address,address,uint256)": "6b2b8dd8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"_delegate\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"}],\"name\":\"addOpts\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"min\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feedAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct FeedList[]\",\"name\":\"feedList\",\"type\":\"tuple[]\"}],\"name\":\"checkFeed\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"min\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feedAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct FeedList[]\",\"name\":\"\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"min\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feedAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct FeedList[]\",\"name\":\"feedList\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"eth\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"total\",\"type\":\"uint256\"}],\"name\":\"feed\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getAdapter\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"holder\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"getBalanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"logicContact\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"removeOperators\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"setAdapter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_new\",\"type\":\"address\"}],\"name\":\"setLogicContact\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawAllEthEx\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"withdrawAllEx\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"tokens\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"withdrawAllToEx\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"withdrawToEx\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"_delegate(address)\":{\"notice\":\"feed end **************\"},\"checkFeed((address,uint256,uint256,uint256,uint256)[])\":{\"notice\":\"feed begin **************\"},\"setAdapter(uint256,address)\":{\"notice\":\"adapter \"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"forge/contracts/Vira.sol\":\"Vira\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"details\":{\"constantOptimizer\":true,\"cse\":true,\"deduplicate\":true,\"inliner\":true,\"jumpdestRemover\":true,\"orderLiterals\":true,\"peephole\":true,\"yul\":false},\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/\",\":ds-test/=forge/lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=forge/lib/forge-std/src/\",\":openzeppelin-contracts/=forge/lib/openzeppelin-contracts/\"]},\"sources\":{\"forge/contracts/IVira.sol\":{\"keccak256\":\"0xcde94902744941463f365c6921b725c660b552dd16991fd0392e6d38fefcb3f1\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://a01139dd955115c64dcae54d0b22649db1994c196f0459add78ee05112478842\",\"dweb:/ipfs/QmcF1k5YE7DgwmvbaJQKGbMtV3cYT6k3JoVeAhcPrCY3fy\"]},\"forge/contracts/Vira.sol\":{\"keccak256\":\"0x2fe34cc82fff26b18952ea7332f82809d253c94b665fd63736e22f3368ecf94a\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://7c59a0549f93489fc836be7f0e537df1bb9e7508bc25a8d99ad10a7d05943907\",\"dweb:/ipfs/QmcUsncAbdRHtZqSJpw6VzsGf7madSqoUeov7Z6hD4tLRk\"]},\"forge/contracts/ViraData.sol\":{\"keccak256\":\"0xdc7a83ececcd58a0bc18bb511e39cfa846fbb7604a0d00f831950b8cbbc8a1c8\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://2e65ad0fd64f82d65a9d6c37a39ebe2c0603717c64db3d2eab864a989eff1196\",\"dweb:/ipfs/QmNm6sU5sn9hwPjv5wCYW956J8uezmNLT67nPmEMp24Cjc\"]},\"forge/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"forge/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.21+commit.d9974bed"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "fallback"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "stateMutability": "payable", "type": "function", "name": "_delegate"}, {"inputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addOpts"}, {"inputs": [{"internalType": "struct FeedList[]", "name": "feedList", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}]}], "stateMutability": "view", "type": "function", "name": "checkFeed", "outputs": [{"internalType": "struct FeedList[]", "name": "", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}]}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "struct FeedList[]", "name": "feedList", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "feedAmount", "type": "uint256"}]}, {"internalType": "address", "name": "eth", "type": "address"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "feed"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getAdapter", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "logicContact", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removeOperators"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setAdapter"}, {"inputs": [{"internalType": "address", "name": "_new", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setLogicContact"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "withdrawAllEthEx"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawAllEx"}, {"inputs": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawAllToEx"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawToEx"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"_delegate(address)": {"notice": "feed end **************"}, "checkFeed((address,uint256,uint256,uint256,uint256)[])": {"notice": "feed begin **************"}, "setAdapter(uint256,address)": {"notice": "adapter "}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=forge/lib/openzeppelin-contracts/contracts/", "ds-test/=forge/lib/forge-std/lib/ds-test/src/", "erc4626-tests/=forge/lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=forge/lib/forge-std/src/", "openzeppelin-contracts/=forge/lib/openzeppelin-contracts/"], "optimizer": {"runs": 200, "details": {"peephole": true, "inliner": true, "jumpdestRemover": true, "orderLiterals": true, "deduplicate": true, "cse": true, "constantOptimizer": true, "yul": false}}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"forge/contracts/Vira.sol": "<PERSON><PERSON>"}, "libraries": {}}, "sources": {"forge/contracts/IVira.sol": {"keccak256": "0xcde94902744941463f365c6921b725c660b552dd16991fd0392e6d38fefcb3f1", "urls": ["bzz-raw://a01139dd955115c64dcae54d0b22649db1994c196f0459add78ee05112478842", "dweb:/ipfs/QmcF1k5YE7DgwmvbaJQKGbMtV3cYT6k3JoVeAhcPrCY3fy"], "license": "UNLICENSED"}, "forge/contracts/Vira.sol": {"keccak256": "0x2fe34cc82fff26b18952ea7332f82809d253c94b665fd63736e22f3368ecf94a", "urls": ["bzz-raw://7c59a0549f93489fc836be7f0e537df1bb9e7508bc25a8d99ad10a7d05943907", "dweb:/ipfs/QmcUsncAbdRHtZqSJpw6VzsGf7madSqoUeov7Z6hD4tLRk"], "license": "UNLICENSED"}, "forge/contracts/ViraData.sol": {"keccak256": "0xdc7a83ececcd58a0bc18bb511e39cfa846fbb7604a0d00f831950b8cbbc8a1c8", "urls": ["bzz-raw://2e65ad0fd64f82d65a9d6c37a39ebe2c0603717c64db3d2eab864a989eff1196", "dweb:/ipfs/QmNm6sU5sn9hwPjv5wCYW956J8uezmNLT67nPmEMp24Cjc"], "license": "UNLICENSED"}, "forge/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "forge/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}}, "version": 1}, "ast": {"absolutePath": "forge/contracts/Vira.sol", "id": 1890, "exportedSymbols": {"Context": [52687], "FeedList": [1396], "IAdapter": [572], "IBeraRouter": [489], "IERC20": [390], "IFactory": [591], "IPoolV1": [431], "IPoolV2": [413], "IRouterV2": [527], "Ownable": [52657], "Roles": [675], "Vira": [1889], "ViraData": [1952]}, "nodeType": "SourceUnit", "src": "39:4371:5", "nodes": [{"id": 1384, "nodeType": "ImportDirective", "src": "39:52:5", "nodes": [], "absolutePath": "forge/lib/openzeppelin-contracts/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "nameLocation": "-1:-1:-1", "scope": 1890, "sourceUnit": 52658, "symbolAliases": [], "unitAlias": ""}, {"id": 1385, "nodeType": "ImportDirective", "src": "120:21:5", "nodes": [], "absolutePath": "forge/contracts/IVira.sol", "file": "./IVira.sol", "nameLocation": "-1:-1:-1", "scope": 1890, "sourceUnit": 676, "symbolAliases": [], "unitAlias": ""}, {"id": 1396, "nodeType": "StructDefinition", "src": "143:116:5", "nodes": [], "canonicalName": "FeedList", "members": [{"constant": false, "id": 1387, "mutability": "mutable", "name": "addr", "nameLocation": "173:4:5", "nodeType": "VariableDeclaration", "scope": 1396, "src": "165:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1386, "name": "address", "nodeType": "ElementaryTypeName", "src": "165:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1389, "mutability": "mutable", "name": "min", "nameLocation": "191:3:5", "nodeType": "VariableDeclaration", "scope": 1396, "src": "183:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1388, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "183:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1391, "mutability": "mutable", "name": "max", "nameLocation": "208:3:5", "nodeType": "VariableDeclaration", "scope": 1396, "src": "200:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1390, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "200:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1393, "mutability": "mutable", "name": "balance", "nameLocation": "225:7:5", "nodeType": "VariableDeclaration", "scope": 1396, "src": "217:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1392, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "217:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1395, "mutability": "mutable", "name": "feedAmount", "nameLocation": "246:10:5", "nodeType": "VariableDeclaration", "scope": 1396, "src": "238:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1394, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "238:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "FeedList", "nameLocation": "150:8:5", "scope": 1890, "visibility": "public"}, {"id": 1397, "nodeType": "PragmaDirective", "src": "261:23:5", "nodes": [], "literals": ["solidity", "^", "0.8", ".0"]}, {"id": 1889, "nodeType": "ContractDefinition", "src": "322:4087:5", "nodes": [{"id": 1403, "nodeType": "UsingForDirective", "src": "353:27:5", "nodes": [], "global": false, "libraryName": {"id": 1400, "name": "Roles", "nameLocations": ["359:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 675, "src": "359:5:5"}, "typeName": {"id": 1402, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1401, "name": "Roles.Role", "nameLocations": ["369:5:5", "375:4:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 597, "src": "369:10:5"}, "referencedDeclaration": 597, "src": "369:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage_ptr", "typeString": "struct Roles.Role"}}}, {"id": 1406, "nodeType": "VariableDeclaration", "src": "386:29:5", "nodes": [], "constant": false, "mutability": "mutable", "name": "_operators", "nameLocation": "405:10:5", "scope": 1889, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role"}, "typeName": {"id": 1405, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1404, "name": "Roles.Role", "nameLocations": ["386:5:5", "392:4:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 597, "src": "386:10:5"}, "referencedDeclaration": 597, "src": "386:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage_ptr", "typeString": "struct Roles.Role"}}, "visibility": "private"}, {"id": 1408, "nodeType": "VariableDeclaration", "src": "421:27:5", "nodes": [], "constant": false, "functionSelector": "d45fe99c", "mutability": "mutable", "name": "logicContact", "nameLocation": "436:12:5", "scope": 1889, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1407, "name": "address", "nodeType": "ElementaryTypeName", "src": "421:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"id": 1412, "nodeType": "VariableDeclaration", "src": "454:36:5", "nodes": [], "constant": false, "mutability": "mutable", "name": "adapters", "nameLocation": "482:8:5", "scope": 1889, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_address_$", "typeString": "mapping(uint256 => address)"}, "typeName": {"id": 1411, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 1409, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "462:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "454:27:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_address_$", "typeString": "mapping(uint256 => address)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 1410, "name": "address", "nodeType": "ElementaryTypeName", "src": "473:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}}, "visibility": "internal"}, {"id": 1415, "nodeType": "VariableDeclaration", "src": "497:65:5", "nodes": [], "constant": true, "mutability": "constant", "name": "MAX_UINT112", "nameLocation": "514:11:5", "scope": 1889, "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}, "typeName": {"id": 1413, "name": "uint112", "nodeType": "ElementaryTypeName", "src": "497:7:5", "typeDescriptions": {"typeIdentifier": "t_uint112", "typeString": "uint112"}}, "value": {"hexValue": "35313932323936383538353334383237363238353330343936333239323230303935", "id": 1414, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "528:34:5", "typeDescriptions": {"typeIdentifier": "t_rational_5192296858534827628530496329220095_by_1", "typeString": "int_const 5192...(26 digits omitted)...0095"}, "value": "5192296858534827628530496329220095"}, "visibility": "internal"}, {"id": 1439, "nodeType": "FunctionDefinition", "src": "569:116:5", "nodes": [], "body": {"id": 1438, "nodeType": "Block", "src": "603:82:5", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 1425, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "628:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1426, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "632:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "628:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1422, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "613:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1424, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "624:3:5", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 623, "src": "613:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$__$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address)"}}, "id": 1427, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "613:26:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1428, "nodeType": "ExpressionStatement", "src": "613:26:5"}, {"expression": {"arguments": [{"arguments": [{"id": 1434, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "672:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}], "id": 1433, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "664:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1432, "name": "address", "nodeType": "ElementaryTypeName", "src": "664:7:5", "typeDescriptions": {}}}, "id": 1435, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "664:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1429, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "649:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1431, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "660:3:5", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 623, "src": "649:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$__$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address)"}}, "id": 1436, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "649:29:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1437, "nodeType": "ExpressionStatement", "src": "649:29:5"}]}, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"expression": {"id": 1418, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "591:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1419, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "595:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "591:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 1420, "kind": "baseConstructorSpecifier", "modifierName": {"id": 1417, "name": "Ownable", "nameLocations": ["583:7:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52657, "src": "583:7:5"}, "nodeType": "ModifierInvocation", "src": "583:19:5"}], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 1416, "nodeType": "ParameterList", "parameters": [], "src": "580:2:5"}, "returnParameters": {"id": 1421, "nodeType": "ParameterList", "parameters": [], "src": "603:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 1452, "nodeType": "ModifierDefinition", "src": "707:128:5", "nodes": [], "body": {"id": 1451, "nodeType": "Block", "src": "731:104:5", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"expression": {"id": 1444, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "764:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1445, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "768:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "764:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1442, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "749:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1443, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "760:3:5", "memberName": "has", "nodeType": "MemberAccess", "referencedDeclaration": 674, "src": "749:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address) view returns (bool)"}}, "id": 1446, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "749:26:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4f70657261746f72733a2063616c6c6572206973206e6f7420746865204f70657261746f72", "id": 1447, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "777:39:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_061617ab0e8877d3b2ca565fe3642685b28cc31a9d33c88b17183f16f87dc578", "typeString": "literal_string \"Operators: caller is not the Operator\""}, "value": "Operators: caller is not the Operator"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_061617ab0e8877d3b2ca565fe3642685b28cc31a9d33c88b17183f16f87dc578", "typeString": "literal_string \"Operators: caller is not the Operator\""}], "id": 1441, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "741:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1448, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "741:76:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1449, "nodeType": "ExpressionStatement", "src": "741:76:5"}, {"id": 1450, "nodeType": "PlaceholderStatement", "src": "827:1:5"}]}, "name": "onlyO<PERSON><PERSON>", "nameLocation": "716:12:5", "parameters": {"id": 1440, "nodeType": "ParameterList", "parameters": [], "src": "728:2:5"}, "virtual": false, "visibility": "internal"}, {"id": 1489, "nodeType": "FunctionDefinition", "src": "841:211:5", "nodes": [], "body": {"id": 1488, "nodeType": "Block", "src": "905:147:5", "nodes": [], "statements": [{"body": {"id": 1486, "nodeType": "Block", "src": "962:84:5", "statements": [{"condition": {"id": 1476, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "980:25:5", "subExpression": {"arguments": [{"baseExpression": {"id": 1472, "name": "operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1455, "src": "992:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 1474, "indexExpression": {"id": 1473, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1461, "src": "1002:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "992:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1471, "name": "isOperator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1516, "src": "981:10:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_bool_$", "typeString": "function (address) view returns (bool)"}}, "id": 1475, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "981:24:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1485, "nodeType": "IfStatement", "src": "976:59:5", "trueBody": {"expression": {"arguments": [{"baseExpression": {"id": 1480, "name": "operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1455, "src": "1022:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 1482, "indexExpression": {"id": 1481, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1461, "src": "1032:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1022:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1477, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "1007:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1479, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1018:3:5", "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 623, "src": "1007:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$__$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address)"}}, "id": 1483, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1007:28:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1484, "nodeType": "ExpressionStatement", "src": "1007:28:5"}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1467, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1464, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1461, "src": "935:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 1465, "name": "operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1455, "src": "939:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 1466, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "949:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "939:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "935:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1487, "initializationExpression": {"assignments": [1461], "declarations": [{"constant": false, "id": 1461, "mutability": "mutable", "name": "i", "nameLocation": "928:1:5", "nodeType": "VariableDeclaration", "scope": 1487, "src": "920:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1460, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "920:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1463, "initialValue": {"hexValue": "30", "id": 1462, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "932:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "920:13:5"}, "loopExpression": {"expression": {"id": 1469, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": true, "src": "957:3:5", "subExpression": {"id": 1468, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1461, "src": "959:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1470, "nodeType": "ExpressionStatement", "src": "957:3:5"}, "nodeType": "ForStatement", "src": "915:131:5"}]}, "functionSelector": "110c6dbf", "implemented": true, "kind": "function", "modifiers": [{"id": 1458, "kind": "modifierInvocation", "modifierName": {"id": 1457, "name": "only<PERSON><PERSON>er", "nameLocations": ["895:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "895:9:5"}, "nodeType": "ModifierInvocation", "src": "895:9:5"}], "name": "addOpts", "nameLocation": "850:7:5", "parameters": {"id": 1456, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1455, "mutability": "mutable", "name": "operators", "nameLocation": "875:9:5", "nodeType": "VariableDeclaration", "scope": 1489, "src": "858:26:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 1453, "name": "address", "nodeType": "ElementaryTypeName", "src": "858:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1454, "nodeType": "ArrayTypeName", "src": "858:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}], "src": "857:28:5"}, "returnParameters": {"id": 1459, "nodeType": "ParameterList", "parameters": [], "src": "905:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1503, "nodeType": "FunctionDefinition", "src": "1058:106:5", "nodes": [], "body": {"id": 1502, "nodeType": "Block", "src": "1120:44:5", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 1499, "name": "operator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1491, "src": "1148:8:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1496, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "1130:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1498, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1141:6:5", "memberName": "remove", "nodeType": "MemberAccess", "referencedDeclaration": 648, "src": "1130:17:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$__$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address)"}}, "id": 1500, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1130:27:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1501, "nodeType": "ExpressionStatement", "src": "1130:27:5"}]}, "functionSelector": "e5486bc3", "implemented": true, "kind": "function", "modifiers": [{"id": 1494, "kind": "modifierInvocation", "modifierName": {"id": 1493, "name": "only<PERSON><PERSON>er", "nameLocations": ["1110:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "1110:9:5"}, "nodeType": "ModifierInvocation", "src": "1110:9:5"}], "name": "removeOperators", "nameLocation": "1067:15:5", "parameters": {"id": 1492, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1491, "mutability": "mutable", "name": "operator", "nameLocation": "1091:8:5", "nodeType": "VariableDeclaration", "scope": 1503, "src": "1083:16:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1490, "name": "address", "nodeType": "ElementaryTypeName", "src": "1083:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1082:18:5"}, "returnParameters": {"id": 1495, "nodeType": "ParameterList", "parameters": [], "src": "1120:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1516, "nodeType": "FunctionDefinition", "src": "1170:113:5", "nodes": [], "body": {"id": 1515, "nodeType": "Block", "src": "1235:48:5", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 1512, "name": "operator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1505, "src": "1267:8:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1510, "name": "_operators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1406, "src": "1252:10:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Role_$597_storage", "typeString": "struct Roles.Role storage ref"}}, "id": 1511, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "1263:3:5", "memberName": "has", "nodeType": "MemberAccess", "referencedDeclaration": 674, "src": "1252:14:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_struct$_Role_$597_storage_ptr_$_t_address_$returns$_t_bool_$attached_to$_t_struct$_Role_$597_storage_ptr_$", "typeString": "function (struct Roles.Role storage pointer,address) view returns (bool)"}}, "id": 1513, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1252:24:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 1509, "id": 1514, "nodeType": "Return", "src": "1245:31:5"}]}, "functionSelector": "6d70f7ae", "implemented": true, "kind": "function", "modifiers": [], "name": "isOperator", "nameLocation": "1179:10:5", "parameters": {"id": 1506, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1505, "mutability": "mutable", "name": "operator", "nameLocation": "1198:8:5", "nodeType": "VariableDeclaration", "scope": 1516, "src": "1190:16:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1504, "name": "address", "nodeType": "ElementaryTypeName", "src": "1190:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1189:18:5"}, "returnParameters": {"id": 1509, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1508, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1516, "src": "1229:4:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1507, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1229:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1228:6:5"}, "scope": 1889, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 1534, "nodeType": "FunctionDefinition", "src": "1289:127:5", "nodes": [], "body": {"id": 1533, "nodeType": "Block", "src": "1354:62:5", "nodes": [], "statements": [{"expression": {"arguments": [{"arguments": [{"id": 1529, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1403:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}], "id": 1528, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1395:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1527, "name": "address", "nodeType": "ElementaryTypeName", "src": "1395:7:5", "typeDescriptions": {}}}, "id": 1530, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1395:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 1524, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1518, "src": "1378:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1523, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "1371:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1525, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1371:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1526, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1385:9:5", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 322, "src": "1371:23:5", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 1531, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1371:38:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1522, "id": 1532, "nodeType": "Return", "src": "1364:45:5"}]}, "functionSelector": "f8b2cb4f", "implemented": true, "kind": "function", "modifiers": [], "name": "getBalance", "nameLocation": "1298:10:5", "parameters": {"id": 1519, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1518, "mutability": "mutable", "name": "token", "nameLocation": "1317:5:5", "nodeType": "VariableDeclaration", "scope": 1534, "src": "1309:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1517, "name": "address", "nodeType": "ElementaryTypeName", "src": "1309:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1308:15:5"}, "returnParameters": {"id": 1522, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1521, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1534, "src": "1345:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1520, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1345:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1344:9:5"}, "scope": 1889, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 1551, "nodeType": "FunctionDefinition", "src": "1422:138:5", "nodes": [], "body": {"id": 1550, "nodeType": "Block", "src": "1505:55:5", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 1547, "name": "holder", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1536, "src": "1546:6:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 1544, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1538, "src": "1529:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1543, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "1522:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1545, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1522:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1546, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1536:9:5", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 322, "src": "1522:23:5", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 1548, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1522:31:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1542, "id": 1549, "nodeType": "Return", "src": "1515:38:5"}]}, "functionSelector": "53290b44", "implemented": true, "kind": "function", "modifiers": [], "name": "getBalanceOf", "nameLocation": "1431:12:5", "parameters": {"id": 1539, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1536, "mutability": "mutable", "name": "holder", "nameLocation": "1452:6:5", "nodeType": "VariableDeclaration", "scope": 1551, "src": "1444:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1535, "name": "address", "nodeType": "ElementaryTypeName", "src": "1444:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1538, "mutability": "mutable", "name": "token", "nameLocation": "1468:5:5", "nodeType": "VariableDeclaration", "scope": 1551, "src": "1460:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1537, "name": "address", "nodeType": "ElementaryTypeName", "src": "1460:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1443:31:5"}, "returnParameters": {"id": 1542, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1541, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1551, "src": "1496:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1540, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1496:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1495:9:5"}, "scope": 1889, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 1571, "nodeType": "FunctionDefinition", "src": "1566:133:5", "nodes": [], "body": {"id": 1570, "nodeType": "Block", "src": "1648:51:5", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 1566, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1555, "src": "1681:2:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 1567, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1557, "src": "1685:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"id": 1563, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1553, "src": "1665:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1562, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "1658:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1564, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1658:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1565, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1672:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 331, "src": "1658:22:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 1568, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1658:34:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1569, "nodeType": "ExpressionStatement", "src": "1658:34:5"}]}, "functionSelector": "6b2b8dd8", "implemented": true, "kind": "function", "modifiers": [{"id": 1560, "kind": "modifierInvocation", "modifierName": {"id": 1559, "name": "only<PERSON><PERSON>er", "nameLocations": ["1638:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "1638:9:5"}, "nodeType": "ModifierInvocation", "src": "1638:9:5"}], "name": "withdrawToEx", "nameLocation": "1575:12:5", "parameters": {"id": 1558, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1553, "mutability": "mutable", "name": "token", "nameLocation": "1596:5:5", "nodeType": "VariableDeclaration", "scope": 1571, "src": "1588:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1552, "name": "address", "nodeType": "ElementaryTypeName", "src": "1588:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1555, "mutability": "mutable", "name": "to", "nameLocation": "1611:2:5", "nodeType": "VariableDeclaration", "scope": 1571, "src": "1603:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1554, "name": "address", "nodeType": "ElementaryTypeName", "src": "1603:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1557, "mutability": "mutable", "name": "amount", "nameLocation": "1623:6:5", "nodeType": "VariableDeclaration", "scope": 1571, "src": "1615:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1556, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1615:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1587:43:5"}, "returnParameters": {"id": 1561, "nodeType": "ParameterList", "parameters": [], "src": "1648:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"id": 1596, "nodeType": "FunctionDefinition", "src": "1705:148:5", "nodes": [], "body": {"id": 1595, "nodeType": "Block", "src": "1762:91:5", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"id": 1582, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "1795:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1583, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1799:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "1795:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [{"arguments": [{"id": 1590, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "1839:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}], "id": 1589, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1831:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1588, "name": "address", "nodeType": "ElementaryTypeName", "src": "1831:7:5", "typeDescriptions": {}}}, "id": 1591, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1831:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"arguments": [{"id": 1585, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1573, "src": "1814:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1584, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "1807:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1586, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1807:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1587, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1821:9:5", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 322, "src": "1807:23:5", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 1592, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1807:38:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"id": 1579, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1573, "src": "1779:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1578, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "1772:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1580, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1772:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1581, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1786:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 331, "src": "1772:22:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 1593, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1772:74:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1594, "nodeType": "ExpressionStatement", "src": "1772:74:5"}]}, "functionSelector": "7793c048", "implemented": true, "kind": "function", "modifiers": [{"id": 1576, "kind": "modifierInvocation", "modifierName": {"id": 1575, "name": "only<PERSON><PERSON>er", "nameLocations": ["1752:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "1752:9:5"}, "nodeType": "ModifierInvocation", "src": "1752:9:5"}], "name": "withdrawAllEx", "nameLocation": "1714:13:5", "parameters": {"id": 1574, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1573, "mutability": "mutable", "name": "token", "nameLocation": "1736:5:5", "nodeType": "VariableDeclaration", "scope": 1596, "src": "1728:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1572, "name": "address", "nodeType": "ElementaryTypeName", "src": "1728:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1727:15:5"}, "returnParameters": {"id": 1577, "nodeType": "ParameterList", "parameters": [], "src": "1762:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1650, "nodeType": "FunctionDefinition", "src": "1859:346:5", "nodes": [], "body": {"id": 1649, "nodeType": "Block", "src": "1940:265:5", "nodes": [], "statements": [{"body": {"id": 1647, "nodeType": "Block", "src": "1990:209:5", "statements": [{"assignments": [1618], "declarations": [{"constant": false, "id": 1618, "mutability": "mutable", "name": "token", "nameLocation": "2011:5:5", "nodeType": "VariableDeclaration", "scope": 1647, "src": "2004:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}, "typeName": {"id": 1617, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1616, "name": "IERC20", "nameLocations": ["2004:6:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 390, "src": "2004:6:5"}, "referencedDeclaration": 390, "src": "2004:6:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "visibility": "internal"}], "id": 1624, "initialValue": {"arguments": [{"baseExpression": {"id": 1620, "name": "tokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1599, "src": "2026:6:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 1622, "indexExpression": {"id": 1621, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1607, "src": "2033:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2026:9:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1619, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "2019:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1623, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2019:17:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "nodeType": "VariableDeclarationStatement", "src": "2004:32:5"}, {"assignments": [1626], "declarations": [{"constant": false, "id": 1626, "mutability": "mutable", "name": "balance", "nameLocation": "2058:7:5", "nodeType": "VariableDeclaration", "scope": 1647, "src": "2050:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1625, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2050:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1634, "initialValue": {"arguments": [{"arguments": [{"id": 1631, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "2092:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}], "id": 1630, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2084:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1629, "name": "address", "nodeType": "ElementaryTypeName", "src": "2084:7:5", "typeDescriptions": {}}}, "id": 1632, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2084:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 1627, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1618, "src": "2068:5:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1628, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2074:9:5", "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 322, "src": "2068:15:5", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 1633, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2068:30:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2050:48:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1637, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1635, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1626, "src": "2116:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 1636, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2126:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2116:11:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1646, "nodeType": "IfStatement", "src": "2112:77:5", "trueBody": {"id": 1645, "nodeType": "Block", "src": "2129:60:5", "statements": [{"expression": {"arguments": [{"id": 1641, "name": "to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1601, "src": "2162:2:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 1642, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1626, "src": "2166:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"id": 1638, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1618, "src": "2147:5:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1640, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2153:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 331, "src": "2147:14:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 1643, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2147:27:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1644, "nodeType": "ExpressionStatement", "src": "2147:27:5"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1612, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1609, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1607, "src": "1966:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 1610, "name": "tokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1599, "src": "1970:6:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[] memory"}}, "id": 1611, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1977:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "1970:13:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1966:17:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1648, "initializationExpression": {"assignments": [1607], "declarations": [{"constant": false, "id": 1607, "mutability": "mutable", "name": "i", "nameLocation": "1963:1:5", "nodeType": "VariableDeclaration", "scope": 1648, "src": "1955:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1606, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1955:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1608, "nodeType": "VariableDeclarationStatement", "src": "1955:9:5"}, "loopExpression": {"expression": {"id": 1614, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1985:3:5", "subExpression": {"id": 1613, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1607, "src": "1985:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1615, "nodeType": "ExpressionStatement", "src": "1985:3:5"}, "nodeType": "ForStatement", "src": "1950:249:5"}]}, "functionSelector": "9b7395e1", "implemented": true, "kind": "function", "modifiers": [{"id": 1604, "kind": "modifierInvocation", "modifierName": {"id": 1603, "name": "only<PERSON><PERSON>er", "nameLocations": ["1930:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "1930:9:5"}, "nodeType": "ModifierInvocation", "src": "1930:9:5"}], "name": "withdrawAllToEx", "nameLocation": "1868:15:5", "parameters": {"id": 1602, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1599, "mutability": "mutable", "name": "tokens", "nameLocation": "1901:6:5", "nodeType": "VariableDeclaration", "scope": 1650, "src": "1884:23:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_memory_ptr", "typeString": "address[]"}, "typeName": {"baseType": {"id": 1597, "name": "address", "nodeType": "ElementaryTypeName", "src": "1884:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1598, "nodeType": "ArrayTypeName", "src": "1884:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_address_$dyn_storage_ptr", "typeString": "address[]"}}, "visibility": "internal"}, {"constant": false, "id": 1601, "mutability": "mutable", "name": "to", "nameLocation": "1917:2:5", "nodeType": "VariableDeclaration", "scope": 1650, "src": "1909:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1600, "name": "address", "nodeType": "ElementaryTypeName", "src": "1909:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1883:37:5"}, "returnParameters": {"id": 1605, "nodeType": "ParameterList", "parameters": [], "src": "1940:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1669, "nodeType": "FunctionDefinition", "src": "2211:123:5", "nodes": [], "body": {"id": 1668, "nodeType": "Block", "src": "2266:68:5", "nodes": [], "statements": [{"expression": {"arguments": [{"expression": {"arguments": [{"id": 1663, "name": "this", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -28, "src": "2313:4:5", "typeDescriptions": {"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_Vira_$1889", "typeString": "contract Vira"}], "id": 1662, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2305:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1661, "name": "address", "nodeType": "ElementaryTypeName", "src": "2305:7:5", "typeDescriptions": {}}}, "id": 1664, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2305:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1665, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2319:7:5", "memberName": "balance", "nodeType": "MemberAccess", "src": "2305:21:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"expression": {"id": 1657, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2284:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 1658, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2288:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "2284:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1656, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2276:8:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_payable_$", "typeString": "type(address payable)"}, "typeName": {"id": 1655, "name": "address", "nodeType": "ElementaryTypeName", "src": "2276:8:5", "stateMutability": "payable", "typeDescriptions": {}}}, "id": 1659, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2276:19:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "id": 1660, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2296:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "src": "2276:28:5", "typeDescriptions": {"typeIdentifier": "t_function_transfer_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 1666, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2276:51:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1667, "nodeType": "ExpressionStatement", "src": "2276:51:5"}]}, "functionSelector": "8642fde2", "implemented": true, "kind": "function", "modifiers": [{"id": 1653, "kind": "modifierInvocation", "modifierName": {"id": 1652, "name": "only<PERSON><PERSON>er", "nameLocations": ["2256:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "2256:9:5"}, "nodeType": "ModifierInvocation", "src": "2256:9:5"}], "name": "withdrawAllEthEx", "nameLocation": "2220:16:5", "parameters": {"id": 1651, "nodeType": "ParameterList", "parameters": [], "src": "2236:2:5"}, "returnParameters": {"id": 1654, "nodeType": "ParameterList", "parameters": [], "src": "2266:0:5"}, "scope": 1889, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 1681, "nodeType": "FunctionDefinition", "src": "2340:94:5", "nodes": [], "body": {"id": 1680, "nodeType": "Block", "src": "2398:36:5", "nodes": [], "statements": [{"expression": {"id": 1678, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1676, "name": "logicContact", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1408, "src": "2408:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1677, "name": "_new", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1671, "src": "2423:4:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2408:19:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1679, "nodeType": "ExpressionStatement", "src": "2408:19:5"}]}, "functionSelector": "a1d448ab", "implemented": true, "kind": "function", "modifiers": [{"id": 1674, "kind": "modifierInvocation", "modifierName": {"id": 1673, "name": "only<PERSON><PERSON>er", "nameLocations": ["2388:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "2388:9:5"}, "nodeType": "ModifierInvocation", "src": "2388:9:5"}], "name": "setLogicContact", "nameLocation": "2349:15:5", "parameters": {"id": 1672, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1671, "mutability": "mutable", "name": "_new", "nameLocation": "2373:4:5", "nodeType": "VariableDeclaration", "scope": 1681, "src": "2365:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1670, "name": "address", "nodeType": "ElementaryTypeName", "src": "2365:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2364:14:5"}, "returnParameters": {"id": 1675, "nodeType": "ParameterList", "parameters": [], "src": "2398:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1698, "nodeType": "FunctionDefinition", "src": "2459:104:5", "nodes": [], "body": {"id": 1697, "nodeType": "Block", "src": "2524:39:5", "nodes": [], "statements": [{"expression": {"id": 1695, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 1691, "name": "adapters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1412, "src": "2534:8:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_address_$", "typeString": "mapping(uint256 => address)"}}, "id": 1693, "indexExpression": {"id": 1692, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1684, "src": "2543:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2534:15:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1694, "name": "addr", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1686, "src": "2552:4:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2534:22:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1696, "nodeType": "ExpressionStatement", "src": "2534:22:5"}]}, "documentation": {"id": 1682, "nodeType": "StructuredDocumentation", "src": "2440:14:5", "text": "adapter "}, "functionSelector": "e26bbe80", "implemented": true, "kind": "function", "modifiers": [{"id": 1689, "kind": "modifierInvocation", "modifierName": {"id": 1688, "name": "only<PERSON><PERSON>er", "nameLocations": ["2514:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52568, "src": "2514:9:5"}, "nodeType": "ModifierInvocation", "src": "2514:9:5"}], "name": "setAdapter", "nameLocation": "2468:10:5", "parameters": {"id": 1687, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1684, "mutability": "mutable", "name": "index", "nameLocation": "2484:5:5", "nodeType": "VariableDeclaration", "scope": 1698, "src": "2479:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1683, "name": "uint", "nodeType": "ElementaryTypeName", "src": "2479:4:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1686, "mutability": "mutable", "name": "addr", "nameLocation": "2499:4:5", "nodeType": "VariableDeclaration", "scope": 1698, "src": "2491:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1685, "name": "address", "nodeType": "ElementaryTypeName", "src": "2491:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2478:26:5"}, "returnParameters": {"id": 1690, "nodeType": "ParameterList", "parameters": [], "src": "2524:0:5"}, "scope": 1889, "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"id": 1710, "nodeType": "FunctionDefinition", "src": "2569:101:5", "nodes": [], "body": {"id": 1709, "nodeType": "Block", "src": "2631:39:5", "nodes": [], "statements": [{"expression": {"baseExpression": {"id": 1705, "name": "adapters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1412, "src": "2648:8:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_address_$", "typeString": "mapping(uint256 => address)"}}, "id": 1707, "indexExpression": {"id": 1706, "name": "index", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1700, "src": "2657:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2648:15:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 1704, "id": 1708, "nodeType": "Return", "src": "2641:22:5"}]}, "functionSelector": "ddeadbb6", "implemented": true, "kind": "function", "modifiers": [], "name": "getAdapter", "nameLocation": "2578:10:5", "parameters": {"id": 1701, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1700, "mutability": "mutable", "name": "index", "nameLocation": "2594:5:5", "nodeType": "VariableDeclaration", "scope": 1710, "src": "2589:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1699, "name": "uint", "nodeType": "ElementaryTypeName", "src": "2589:4:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2588:12:5"}, "returnParameters": {"id": 1704, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1703, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1710, "src": "2622:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1702, "name": "address", "nodeType": "ElementaryTypeName", "src": "2622:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2621:9:5"}, "scope": 1889, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 1790, "nodeType": "FunctionDefinition", "src": "2726:533:5", "nodes": [], "body": {"id": 1789, "nodeType": "Block", "src": "2822:437:5", "nodes": [], "statements": [{"assignments": [1725], "declarations": [{"constant": false, "id": 1725, "mutability": "mutable", "name": "total", "nameLocation": "2840:5:5", "nodeType": "VariableDeclaration", "scope": 1789, "src": "2832:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1724, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2832:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1727, "initialValue": {"hexValue": "30", "id": 1726, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2848:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "2832:17:5"}, {"body": {"id": 1783, "nodeType": "Block", "src": "2901:318:5", "statements": [{"assignments": [1739], "declarations": [{"constant": false, "id": 1739, "mutability": "mutable", "name": "balance", "nameLocation": "2923:7:5", "nodeType": "VariableDeclaration", "scope": 1783, "src": "2915:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1738, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2915:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1745, "initialValue": {"expression": {"expression": {"baseExpression": {"id": 1740, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "2933:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1742, "indexExpression": {"id": 1741, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "2942:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2933:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_memory_ptr", "typeString": "struct FeedList memory"}}, "id": 1743, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2945:4:5", "memberName": "addr", "nodeType": "MemberAccess", "referencedDeclaration": 1387, "src": "2933:16:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1744, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2950:7:5", "memberName": "balance", "nodeType": "MemberAccess", "src": "2933:24:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2915:42:5"}, {"expression": {"id": 1751, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"baseExpression": {"id": 1746, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "2971:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1748, "indexExpression": {"id": 1747, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "2980:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2971:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_memory_ptr", "typeString": "struct FeedList memory"}}, "id": 1749, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "2983:7:5", "memberName": "balance", "nodeType": "MemberAccess", "referencedDeclaration": 1393, "src": "2971:19:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1750, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1739, "src": "2993:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2971:29:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1752, "nodeType": "ExpressionStatement", "src": "2971:29:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1758, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1753, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1739, "src": "3018:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"baseExpression": {"id": 1754, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "3028:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1756, "indexExpression": {"id": 1755, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "3037:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3028:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_memory_ptr", "typeString": "struct FeedList memory"}}, "id": 1757, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3040:3:5", "memberName": "min", "nodeType": "MemberAccess", "referencedDeclaration": 1389, "src": "3028:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3018:25:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1782, "nodeType": "IfStatement", "src": "3014:195:5", "trueBody": {"id": 1781, "nodeType": "Block", "src": "3045:164:5", "statements": [{"assignments": [1760], "declarations": [{"constant": false, "id": 1760, "mutability": "mutable", "name": "amount", "nameLocation": "3071:6:5", "nodeType": "VariableDeclaration", "scope": 1781, "src": "3063:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1759, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3063:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1767, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1766, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"baseExpression": {"id": 1761, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "3080:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1763, "indexExpression": {"id": 1762, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "3089:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3080:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_memory_ptr", "typeString": "struct FeedList memory"}}, "id": 1764, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3092:3:5", "memberName": "max", "nodeType": "MemberAccess", "referencedDeclaration": 1391, "src": "3080:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 1765, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1739, "src": "3098:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3080:25:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3063:42:5"}, {"expression": {"id": 1772, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1768, "name": "total", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1725, "src": "3123:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1769, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1760, "src": "3131:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 1770, "name": "total", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1725, "src": "3140:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3131:14:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3123:22:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1773, "nodeType": "ExpressionStatement", "src": "3123:22:5"}, {"expression": {"id": 1779, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"baseExpression": {"id": 1774, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "3163:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1776, "indexExpression": {"id": 1775, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "3172:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3163:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_memory_ptr", "typeString": "struct FeedList memory"}}, "id": 1777, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "3175:10:5", "memberName": "feedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 1395, "src": "3163:22:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1778, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1760, "src": "3188:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3163:31:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1780, "nodeType": "ExpressionStatement", "src": "3163:31:5"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1734, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1731, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "2875:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 1732, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "2879:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, "id": 1733, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2888:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "2879:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2875:19:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1784, "initializationExpression": {"assignments": [1729], "declarations": [{"constant": false, "id": 1729, "mutability": "mutable", "name": "i", "nameLocation": "2872:1:5", "nodeType": "VariableDeclaration", "scope": 1784, "src": "2864:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1728, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2864:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1730, "nodeType": "VariableDeclarationStatement", "src": "2864:9:5"}, "loopExpression": {"expression": {"id": 1736, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2896:3:5", "subExpression": {"id": 1735, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1729, "src": "2896:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1737, "nodeType": "ExpressionStatement", "src": "2896:3:5"}, "nodeType": "ForStatement", "src": "2859:360:5"}, {"expression": {"components": [{"id": 1785, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1715, "src": "3236:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList memory[] memory"}}, {"id": 1786, "name": "total", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1725, "src": "3246:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 1787, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3235:17:5", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr_$_t_uint256_$", "typeString": "tuple(struct FeedList memory[] memory,uint256)"}}, "functionReturnParameters": 1723, "id": 1788, "nodeType": "Return", "src": "3228:24:5"}]}, "documentation": {"id": 1711, "nodeType": "StructuredDocumentation", "src": "2677:44:5", "text": " feed begin **************"}, "functionSelector": "c8de451d", "implemented": true, "kind": "function", "modifiers": [], "name": "checkFeed", "nameLocation": "2735:9:5", "parameters": {"id": 1716, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1715, "mutability": "mutable", "name": "feedList", "nameLocation": "2763:8:5", "nodeType": "VariableDeclaration", "scope": 1790, "src": "2745:26:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList[]"}, "typeName": {"baseType": {"id": 1713, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1712, "name": "FeedList", "nameLocations": ["2745:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 1396, "src": "2745:8:5"}, "referencedDeclaration": 1396, "src": "2745:8:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_storage_ptr", "typeString": "struct FeedList"}}, "id": 1714, "nodeType": "ArrayTypeName", "src": "2745:10:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_storage_$dyn_storage_ptr", "typeString": "struct FeedList[]"}}, "visibility": "internal"}], "src": "2744:28:5"}, "returnParameters": {"id": 1723, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1720, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1790, "src": "2794:17:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_memory_ptr_$dyn_memory_ptr", "typeString": "struct FeedList[]"}, "typeName": {"baseType": {"id": 1718, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1717, "name": "FeedList", "nameLocations": ["2794:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 1396, "src": "2794:8:5"}, "referencedDeclaration": 1396, "src": "2794:8:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_storage_ptr", "typeString": "struct FeedList"}}, "id": 1719, "nodeType": "ArrayTypeName", "src": "2794:10:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_storage_$dyn_storage_ptr", "typeString": "struct FeedList[]"}}, "visibility": "internal"}, {"constant": false, "id": 1722, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1790, "src": "2813:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1721, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2813:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2793:28:5"}, "scope": 1889, "stateMutability": "view", "virtual": false, "visibility": "public"}, {"id": 1856, "nodeType": "FunctionDefinition", "src": "3265:488:5", "nodes": [], "body": {"id": 1855, "nodeType": "Block", "src": "3367:386:5", "nodes": [], "statements": [{"assignments": [1804], "declarations": [{"constant": false, "id": 1804, "mutability": "mutable", "name": "balance", "nameLocation": "3385:7:5", "nodeType": "VariableDeclaration", "scope": 1855, "src": "3377:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1803, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3377:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1808, "initialValue": {"arguments": [{"id": 1806, "name": "eth", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1796, "src": "3406:3:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1805, "name": "getBalance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1534, "src": "3395:10:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view returns (uint256)"}}, "id": 1807, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3395:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3377:33:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1812, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1810, "name": "balance", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1804, "src": "3428:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 1811, "name": "total", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1798, "src": "3438:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3428:15:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "6e6f7420656e6f7567682062616c616e6365", "id": 1813, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3445:20:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_596712763af5ad819c1a1c8db05ac93e50918d28937626717ff5e1d919b4454a", "typeString": "literal_string \"not enough balance\""}, "value": "not enough balance"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_596712763af5ad819c1a1c8db05ac93e50918d28937626717ff5e1d919b4454a", "typeString": "literal_string \"not enough balance\""}], "id": 1809, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3420:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1814, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3420:46:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1815, "nodeType": "ExpressionStatement", "src": "3420:46:5"}, {"expression": {"arguments": [{"id": 1820, "name": "total", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1798, "src": "3497:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"arguments": [{"id": 1817, "name": "eth", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1796, "src": "3483:3:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1816, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "3476:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$390_$", "typeString": "type(contract IERC20)"}}, "id": 1818, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3476:11:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$390", "typeString": "contract IERC20"}}, "id": 1819, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3488:8:5", "memberName": "withdraw", "nodeType": "MemberAccess", "referencedDeclaration": 370, "src": "3476:20:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 1821, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3476:27:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1822, "nodeType": "ExpressionStatement", "src": "3476:27:5"}, {"body": {"id": 1853, "nodeType": "Block", "src": "3555:192:5", "statements": [{"assignments": [1834, null], "declarations": [{"constant": false, "id": 1834, "mutability": "mutable", "name": "sent", "nameLocation": "3620:4:5", "nodeType": "VariableDeclaration", "scope": 1853, "src": "3615:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1833, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3615:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, null], "id": 1847, "initialValue": {"arguments": [{"hexValue": "", "id": 1845, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3682:2:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}, "value": ""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_c5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470", "typeString": "literal_string \"\""}], "expression": {"expression": {"baseExpression": {"id": 1835, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1794, "src": "3629:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct FeedList calldata[] calldata"}}, "id": 1837, "indexExpression": {"id": 1836, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1824, "src": "3638:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3629:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_calldata_ptr", "typeString": "struct FeedList calldata"}}, "id": 1838, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3641:4:5", "memberName": "addr", "nodeType": "MemberAccess", "referencedDeclaration": 1387, "src": "3629:16:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 1839, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3646:4:5", "memberName": "call", "nodeType": "MemberAccess", "src": "3629:21:5", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 1844, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "names": ["value"], "nodeType": "FunctionCallOptions", "options": [{"expression": {"baseExpression": {"id": 1840, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1794, "src": "3658:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct FeedList calldata[] calldata"}}, "id": 1842, "indexExpression": {"id": 1841, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1824, "src": "3667:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3658:11:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_calldata_ptr", "typeString": "struct FeedList calldata"}}, "id": 1843, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3670:10:5", "memberName": "feedAmount", "nodeType": "MemberAccess", "referencedDeclaration": 1395, "src": "3658:22:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "src": "3629:52:5", "typeDescriptions": {"typeIdentifier": "t_function_barecall_payable$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$value", "typeString": "function (bytes memory) payable returns (bool,bytes memory)"}}, "id": 1846, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3629:56:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "3614:71:5"}, {"expression": {"arguments": [{"id": 1849, "name": "sent", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1834, "src": "3707:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4661696c656420746f2073656e64204574686572", "id": 1850, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3713:22:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_445140255c9d889994129d349e64078d6f76b4b37ec896948f7e858f9b8a0dcb", "typeString": "literal_string \"Failed to send <PERSON><PERSON>\""}, "value": "Failed to send <PERSON><PERSON>"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_445140255c9d889994129d349e64078d6f76b4b37ec896948f7e858f9b8a0dcb", "typeString": "literal_string \"Failed to send <PERSON><PERSON>\""}], "id": 1848, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3699:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1851, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3699:37:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1852, "nodeType": "ExpressionStatement", "src": "3699:37:5"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1829, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1826, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1824, "src": "3529:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 1827, "name": "feedList", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1794, "src": "3533:8:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct FeedList calldata[] calldata"}}, "id": 1828, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3542:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "3533:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3529:19:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1854, "initializationExpression": {"assignments": [1824], "declarations": [{"constant": false, "id": 1824, "mutability": "mutable", "name": "i", "nameLocation": "3526:1:5", "nodeType": "VariableDeclaration", "scope": 1854, "src": "3518:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1823, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3518:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1825, "nodeType": "VariableDeclarationStatement", "src": "3518:9:5"}, "loopExpression": {"expression": {"id": 1831, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3550:3:5", "subExpression": {"id": 1830, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1824, "src": "3550:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1832, "nodeType": "ExpressionStatement", "src": "3550:3:5"}, "nodeType": "ForStatement", "src": "3513:234:5"}]}, "functionSelector": "aa239e1d", "implemented": true, "kind": "function", "modifiers": [{"id": 1801, "kind": "modifierInvocation", "modifierName": {"id": 1800, "name": "onlyO<PERSON><PERSON>", "nameLocations": ["3354:12:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 1452, "src": "3354:12:5"}, "nodeType": "ModifierInvocation", "src": "3354:12:5"}], "name": "feed", "nameLocation": "3274:4:5", "parameters": {"id": 1799, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1794, "mutability": "mutable", "name": "feedList", "nameLocation": "3299:8:5", "nodeType": "VariableDeclaration", "scope": 1856, "src": "3279:28:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_calldata_ptr_$dyn_calldata_ptr", "typeString": "struct FeedList[]"}, "typeName": {"baseType": {"id": 1792, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1791, "name": "FeedList", "nameLocations": ["3279:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 1396, "src": "3279:8:5"}, "referencedDeclaration": 1396, "src": "3279:8:5", "typeDescriptions": {"typeIdentifier": "t_struct$_FeedList_$1396_storage_ptr", "typeString": "struct FeedList"}}, "id": 1793, "nodeType": "ArrayTypeName", "src": "3279:10:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_FeedList_$1396_storage_$dyn_storage_ptr", "typeString": "struct FeedList[]"}}, "visibility": "internal"}, {"constant": false, "id": 1796, "mutability": "mutable", "name": "eth", "nameLocation": "3317:3:5", "nodeType": "VariableDeclaration", "scope": 1856, "src": "3309:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1795, "name": "address", "nodeType": "ElementaryTypeName", "src": "3309:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1798, "mutability": "mutable", "name": "total", "nameLocation": "3330:5:5", "nodeType": "VariableDeclaration", "scope": 1856, "src": "3322:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1797, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3322:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3278:58:5"}, "returnParameters": {"id": 1802, "nodeType": "ParameterList", "parameters": [], "src": "3367:0:5"}, "scope": 1889, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 1876, "nodeType": "FunctionDefinition", "src": "3806:492:5", "nodes": [], "body": {"id": 1875, "nodeType": "Block", "src": "3877:421:5", "nodes": [], "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 1870, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1865, "name": "implementation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1859, "src": "3895:14:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 1868, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3921:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 1867, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3913:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 1866, "name": "address", "nodeType": "ElementaryTypeName", "src": "3913:7:5", "typeDescriptions": {}}}, "id": 1869, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3913:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3895:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "6572726f72206c6f6769632061646472657373", "id": 1871, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3925:21:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_592816677ecd558a9215c3fe04ba736c29e3a28389a0ceb3eed09609f09fb167", "typeString": "literal_string \"error logic address\""}, "value": "error logic address"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_592816677ecd558a9215c3fe04ba736c29e3a28389a0ceb3eed09609f09fb167", "typeString": "literal_string \"error logic address\""}], "id": 1864, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3887:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1872, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3887:60:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1873, "nodeType": "ExpressionStatement", "src": "3887:60:5"}, {"AST": {"nativeSrc": "3966:326:5", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3966:326:5", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "3993:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3993:1:5", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "3996:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3996:1:5", "type": "", "value": "0"}, {"arguments": [], "functionName": {"name": "calldatasize", "nativeSrc": "3999:12:5", "nodeType": "YulIdentifier", "src": "3999:12:5"}, "nativeSrc": "3999:14:5", "nodeType": "YulFunctionCall", "src": "3999:14:5"}], "functionName": {"name": "calldatacopy", "nativeSrc": "3980:12:5", "nodeType": "YulIdentifier", "src": "3980:12:5"}, "nativeSrc": "3980:34:5", "nodeType": "YulFunctionCall", "src": "3980:34:5"}, "nativeSrc": "3980:34:5", "nodeType": "YulExpressionStatement", "src": "3980:34:5"}, {"nativeSrc": "4027:74:5", "nodeType": "YulVariableDeclaration", "src": "4027:74:5", "value": {"arguments": [{"arguments": [], "functionName": {"name": "gas", "nativeSrc": "4054:3:5", "nodeType": "YulIdentifier", "src": "4054:3:5"}, "nativeSrc": "4054:5:5", "nodeType": "YulFunctionCall", "src": "4054:5:5"}, {"name": "implementation", "nativeSrc": "4061:14:5", "nodeType": "YulIdentifier", "src": "4061:14:5"}, {"kind": "number", "nativeSrc": "4077:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4077:1:5", "type": "", "value": "0"}, {"arguments": [], "functionName": {"name": "calldatasize", "nativeSrc": "4080:12:5", "nodeType": "YulIdentifier", "src": "4080:12:5"}, "nativeSrc": "4080:14:5", "nodeType": "YulFunctionCall", "src": "4080:14:5"}, {"kind": "number", "nativeSrc": "4096:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4096:1:5", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "4099:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4099:1:5", "type": "", "value": "0"}], "functionName": {"name": "delegatecall", "nativeSrc": "4041:12:5", "nodeType": "YulIdentifier", "src": "4041:12:5"}, "nativeSrc": "4041:60:5", "nodeType": "YulFunctionCall", "src": "4041:60:5"}, "variables": [{"name": "result", "nativeSrc": "4031:6:5", "nodeType": "YulTypedName", "src": "4031:6:5", "type": ""}]}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "4129:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4129:1:5", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "4132:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4132:1:5", "type": "", "value": "0"}, {"arguments": [], "functionName": {"name": "returndatasize", "nativeSrc": "4135:14:5", "nodeType": "YulIdentifier", "src": "4135:14:5"}, "nativeSrc": "4135:16:5", "nodeType": "YulFunctionCall", "src": "4135:16:5"}], "functionName": {"name": "returndatacopy", "nativeSrc": "4114:14:5", "nodeType": "YulIdentifier", "src": "4114:14:5"}, "nativeSrc": "4114:38:5", "nodeType": "YulFunctionCall", "src": "4114:38:5"}, "nativeSrc": "4114:38:5", "nodeType": "YulExpressionStatement", "src": "4114:38:5"}, {"cases": [{"body": {"nativeSrc": "4199:31:5", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4199:31:5", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "4208:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4208:1:5", "type": "", "value": "0"}, {"arguments": [], "functionName": {"name": "returndatasize", "nativeSrc": "4211:14:5", "nodeType": "YulIdentifier", "src": "4211:14:5"}, "nativeSrc": "4211:16:5", "nodeType": "YulFunctionCall", "src": "4211:16:5"}], "functionName": {"name": "revert", "nativeSrc": "4201:6:5", "nodeType": "YulIdentifier", "src": "4201:6:5"}, "nativeSrc": "4201:27:5", "nodeType": "YulFunctionCall", "src": "4201:27:5"}, "nativeSrc": "4201:27:5", "nodeType": "YulExpressionStatement", "src": "4201:27:5"}]}, "nativeSrc": "4192:38:5", "nodeType": "YulCase", "src": "4192:38:5", "value": {"kind": "number", "nativeSrc": "4197:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4197:1:5", "type": "", "value": "0"}}, {"body": {"nativeSrc": "4251:31:5", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4251:31:5", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "4260:1:5", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4260:1:5", "type": "", "value": "0"}, {"arguments": [], "functionName": {"name": "returndatasize", "nativeSrc": "4263:14:5", "nodeType": "YulIdentifier", "src": "4263:14:5"}, "nativeSrc": "4263:16:5", "nodeType": "YulFunctionCall", "src": "4263:16:5"}], "functionName": {"name": "return", "nativeSrc": "4253:6:5", "nodeType": "YulIdentifier", "src": "4253:6:5"}, "nativeSrc": "4253:27:5", "nodeType": "YulFunctionCall", "src": "4253:27:5"}, "nativeSrc": "4253:27:5", "nodeType": "YulExpressionStatement", "src": "4253:27:5"}]}, "nativeSrc": "4243:39:5", "nodeType": "YulCase", "src": "4243:39:5", "value": "default"}], "expression": {"name": "result", "nativeSrc": "4173:6:5", "nodeType": "YulIdentifier", "src": "4173:6:5"}, "nativeSrc": "4166:116:5", "nodeType": "YulSwitch", "src": "4166:116:5"}]}, "evmVersion": "shanghai", "externalReferences": [{"declaration": 1859, "isOffset": false, "isSlot": false, "src": "4061:14:5", "valueSize": 1}], "id": 1874, "nodeType": "InlineAssembly", "src": "3957:335:5"}]}, "documentation": {"id": 1857, "nodeType": "StructuredDocumentation", "src": "3758:42:5", "text": " feed end **************"}, "functionSelector": "f13101e9", "implemented": true, "kind": "function", "modifiers": [{"id": 1862, "kind": "modifierInvocation", "modifierName": {"id": 1861, "name": "onlyO<PERSON><PERSON>", "nameLocations": ["3864:12:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 1452, "src": "3864:12:5"}, "nodeType": "ModifierInvocation", "src": "3864:12:5"}], "name": "_delegate", "nameLocation": "3815:9:5", "parameters": {"id": 1860, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1859, "mutability": "mutable", "name": "implementation", "nameLocation": "3833:14:5", "nodeType": "VariableDeclaration", "scope": 1876, "src": "3825:22:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1858, "name": "address", "nodeType": "ElementaryTypeName", "src": "3825:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3824:24:5"}, "returnParameters": {"id": 1863, "nodeType": "ParameterList", "parameters": [], "src": "3877:0:5"}, "scope": 1889, "stateMutability": "payable", "virtual": false, "visibility": "public"}, {"id": 1880, "nodeType": "FunctionDefinition", "src": "4304:29:5", "nodes": [], "body": {"id": 1879, "nodeType": "Block", "src": "4331:2:5", "nodes": [], "statements": []}, "implemented": true, "kind": "receive", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 1877, "nodeType": "ParameterList", "parameters": [], "src": "4311:2:5"}, "returnParameters": {"id": 1878, "nodeType": "ParameterList", "parameters": [], "src": "4331:0:5"}, "scope": 1889, "stateMutability": "payable", "virtual": false, "visibility": "external"}, {"id": 1888, "nodeType": "FunctionDefinition", "src": "4339:68:5", "nodes": [], "body": {"id": 1887, "nodeType": "Block", "src": "4367:40:5", "nodes": [], "statements": [{"expression": {"arguments": [{"id": 1884, "name": "logicContact", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1408, "src": "4387:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 1883, "name": "_delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1876, "src": "4377:9:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 1885, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4377:23:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1886, "nodeType": "ExpressionStatement", "src": "4377:23:5"}]}, "implemented": true, "kind": "fallback", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "parameters": {"id": 1881, "nodeType": "ParameterList", "parameters": [], "src": "4347:2:5"}, "returnParameters": {"id": 1882, "nodeType": "ParameterList", "parameters": [], "src": "4367:0:5"}, "scope": 1889, "stateMutability": "payable", "virtual": false, "visibility": "external"}], "abstract": false, "baseContracts": [{"baseName": {"id": 1398, "name": "Ownable", "nameLocations": ["339:7:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 52657, "src": "339:7:5"}, "id": 1399, "nodeType": "InheritanceSpecifier", "src": "339:7:5"}], "canonicalName": "<PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "linearizedBaseContracts": [1889, 52657, 52687], "name": "<PERSON><PERSON>", "nameLocation": "331:4:5", "scope": 1890, "usedErrors": [52523, 52528], "usedEvents": [52534]}], "license": "UNLICENSED"}, "id": 5}