use std::{collections::HashMap, str::FromStr};
use alloy::{primitives::{utils, Address, U256}, providers::WalletProvider, sol};
use tracing::level_filters::STATIC_MAX_LEVEL;
use crate::{connector::{Connector, ViraProvider}, executor::gas::{Gas, GasTrait}, user::User, vira::{consts::{UPDATE_BALANCE_RETRY, U_900005}, contract::IViraBase::IViraBaseInstance, errors::DEXError, util, Vira}, CONFIG, USER, VIRA};
use IVira::IViraInstance;

sol! (
    #[derive(Debug)]
    #[allow(missing_docs)]
    #[sol(rpc)]
    IVira,
    "src/vira/contract/ViraLogic.json"
);

sol! (
    #[derive(Debug)]
    #[allow(missing_docs)]
    #[sol(rpc)]
    IViraBase,
    "src/vira/contract/Vira.json"
);

sol! {
    #[derive(Debug)]
    #[sol(rpc)]
    interface IERC20 {
        function balanceOf(address account) external view returns (uint256);
    }
}
#[derive(Debug, Clone)]
pub struct Contract {
    pub owner : Address,
    pub addr : Address,
    pub contract : IViraInstance<ViraProvider>,
    pub base_contract : IViraBaseInstance<ViraProvider>,
    pub provider : ViraProvider,
}

/*
impl Clone for Contract {
    fn clone(&self) -> Self {
        Contract {
            owner: self.owner,
            addr: self.addr,
            connector: self.connector.clone(),
        }
    }
}
 */

impl Contract {

    pub async fn new(addr : Address, provider : ViraProvider) -> Result<Contract, Box<dyn std::error::Error>> {
        let contract = IVira::new(addr, provider.clone());
        let base_contract = IViraBase::new(addr, provider.clone());
        let owner = contract.owner().call().await?;
        println!("合约所有者已更新: {}", owner);

        Ok(Contract {
            owner : owner,
            addr,
            provider,
            contract,
            base_contract
        })
    }

    pub async fn update_balance(&self) -> Result<(), Box<dyn std::error::Error>> {
        let balance = self.get_balance().await?;
        *USER.balances.write().unwrap() = balance;
        Ok(())
    }

    pub async fn get_balance(&self) -> Result<HashMap<Address, U256>, Box<dyn std::error::Error>> {
        let mut balances = HashMap::new();
        // 遍历所有稳定币地址
        for &stable_addr in CONFIG.stables.iter() {
            // 创建ERC20合约实例
            let token = IERC20::new(stable_addr, &self.provider);
            // 调用合约获取余额
            // Retry the balance fetching in case of an error
            let mut attempts = 0;
            let mut success = false;
            
            while attempts < UPDATE_BALANCE_RETRY && !success {
                attempts += 1;
                match token.balanceOf(self.addr).call().await {
                    Ok(result) => {
                        balances.insert(stable_addr, result);
                        if let Some(t) = CONFIG.tokens.get(&stable_addr) {
                            println!(" ({}) {}", t.name, utils::format_units(result, t.decimals).unwrap());
                        }
                        success = true;
                    },
                    Err(e) => {
                        println!("Attempt {}: Failed to get balance for stable token {}: {:?}", attempts, stable_addr, e);
                        if attempts == UPDATE_BALANCE_RETRY {
                            println!("Exceeded maximum retry attempts for stable token {}", stable_addr);
                            panic!("update balance error");
                        } else {
                            // Wait before retrying
                            util::delay(3000).await;
                        }
                    }
                }
            }
        }

        Ok(balances)
    }

    pub async fn batch_check_pair_fee(&self, inputs: Vec<ViraLogic::CheckPairFeeInputDesc>) -> Result<Vec<Vec<U256>>, DEXError> {
        let batch_size = inputs.len();
        if batch_size == 0 {
            return Ok(Vec::new());
        }

        let balance = USER.balances.read().unwrap().clone();
        let (stable_addrs, stable_amounts) = User::split_amounts(balance, Some(batch_size * 2));
        // 先尝试批量请求
        let batch_result = self.contract.batchCheckPairFee(inputs.clone(), stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await;

        //println!("batch_check_pair_fee: batch({}), stable_addrs: {:?}, stable_amounts: {:?}, owner : {}", batch_size, stable_addrs, stable_amounts, self.owner);

        let mut fallback = false;
        match &batch_result {
            Ok(res) => {
                if res.len() != inputs.len() {
                    println!("Batch request returned partial data (expected {}, got {}), retrying with individual requests", inputs.len(), res.len());
                    fallback = true;
                }
            },
            Err(e) => {
                println!("Batch request failed: {:?}, retrying with individual requests", e);
                fallback = true;
            }
        }

        if !fallback {
            return Ok(batch_result.unwrap());
        }

        // Fallback: 逐个请求
        util::delay(2000).await;
        let mut all_fees = Vec::new();
        for input in inputs {
            let addr = &input.pair[0].addr.clone();
            match self.contract.batchCheckPairFee(vec![input], stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
                Ok(res) => all_fees.extend(res),
                Err(e) => {
                    println!("Individual request failed: {}, {:?}", addr, e);
                    // - 900005: 未知原因
                    all_fees.push(vec![U_900005]);
                }
            }
        }
        Ok(all_fees)
    }
    
    /// 批量检查MEV路径的费用和状态，支持降级机制
    ///
    /// 该函数首先尝试批量处理所有MEV请求，如果批量处理失败，
    /// 会自动降级为单个请求逐一处理，确保系统的容错性。
    ///
    /// # 参数
    /// * `inputs` - MEV请求数组，每个元素是一个 Vec<ViraData::PoolReq>
    ///
    /// # 返回值
    /// * `Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>` - 成功时返回检查结果数组
    ///
    /// # 降级机制
    /// 1. 首先尝试批量处理所有请求
    /// 2. 如果批量处理失败，记录错误并启动降级机制
    /// 3. 将批量请求拆分为单个请求，逐一处理
    /// 4. 收集所有单个请求的结果，合并返回
    /// 5. 对于单个请求失败的情况，创建空结果以保持数组长度一致
    pub async fn batch_check_mev(&self, inputs: Vec<Vec<ViraData::PoolReq>>) -> Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError> {
        let batch_size = inputs.len();
        if batch_size == 0 {
            return Ok(Vec::new());
        }
        let balance = USER.balances.read().unwrap().clone();
        let (stable_addrs, stable_amounts) = User::split_amounts(balance, Some(batch_size * 2));
        
        // println!("batch_check_mev: batch({}), stable_addrs: {:?}, stable_amounts: {:?}, owner : {}", batch_size, stable_addrs, stable_amounts, self.owner);

        // 首先尝试批量处理
        let batch_result = self.contract.batchCheckMev(inputs.clone(), stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await;

        inputs.iter().for_each(|reqs| {
            if reqs.len() == 2 && reqs.iter().any(|r| r.addr == Address::from_str("0x32d6b77ee026d471c191ea9ef9ed404d2cb4d4a6").unwrap()) {
                println!("MEV请求: {:?}", reqs);
            }
        });

        let mut fallback = false;
        match &batch_result {
            Ok(res) => {
                if res.len() != inputs.len() {
                    println!("❌ 批量MEV检查返回部分数据 (预期 {}, 得到 {}), 启动降级机制", inputs.len(), res.len());
                    fallback = true;
                }
            },
            Err(e) => {
                println!("❌ 批量MEV检查失败，启动降级机制: {:?}", e);
                fallback = true;
            }
        }

        if !fallback {
            //println!("✅ 批量MEV检查成功，处理了 {} 个MEV路径", batch_size);
            return Ok(batch_result.unwrap());
        }
        
        println!("   - 合约地址: {}", self.addr);
        println!("   - 批次大小: {}", batch_size);
        println!("   - 正在将批量请求拆分为单个请求进行处理...");

        // 等待2秒后开始降级处理
        util::delay(2000).await;

        // 降级机制：逐个处理每个MEV请求
        let mut all_results = Vec::with_capacity(batch_size);
        let mut success_count = 0;
        let mut failed_count = 0;

        for (index, single_input) in inputs.into_iter().enumerate() {
            match self.contract.batchCheckMev(
                vec![single_input],
                stable_addrs.clone(),
                stable_amounts.clone()
            ).from(self.owner).call().await {
                Ok(mut results) => {
                    if !results.is_empty() {
                        all_results.push(results.remove(0));
                        success_count += 1;
                    } else {
                        // 合约返回空结果，创建默认空结果
                        all_results.push(ViraLogic::CheckMevsResultDesc {
                            gas: U256::ZERO,
                            fee0: vec![],
                            fee1: vec![],
                        });
                        failed_count += 1;
                        println!("   - ❌❌ 单个请求 {}/{}: 合约返回空结果", index + 1, batch_size);
                    }
                }
                Err(single_e) => {
                    // 单个请求也失败，创建空结果以保持数组长度一致
                    all_results.push(ViraLogic::CheckMevsResultDesc {
                        gas: U256::ZERO,
                        fee0: vec![],
                        fee1: vec![],
                    });
                    failed_count += 1;
                    println!("   - ❌❌ 单个请求 {}/{} 失败: {:?}", index + 1, batch_size, single_e);
                }
            }

            // 每处理20个请求显示一次进度
            if (index + 1) % 20 == 0 || index + 1 == batch_size {
                println!("   - 降级处理进度: {}/{} (成功: {}, 失败: {})",
                    index + 1, batch_size, success_count, failed_count);
            }
        }

        println!("✅ 降级机制完成，总计处理: {} 个请求 (成功: {}, 失败: {})",
            batch_size, success_count, failed_count);

        Ok(all_results)
    }


    pub async fn batch_feed() -> Result<(), DEXError>  {
        let contract = &VIRA.get().unwrap().contract;
        let base = &contract.base_contract;
        let provider = &contract.provider;
        
        let addrs = &CONFIG.get_all_op_addr();
        //使用alloy的parser把gwei转成u256的gas值
        let max_feed : U256 = utils::parse_ether(&CONFIG.max_gas_feed.to_string()).expect("parse gas error").into();
        //min_feed = 80%的max_feed
        let min_feed = max_feed * U256::from(80) / U256::from(100);

        let feed_list : Vec<IViraBase::FeedList> = addrs.iter().map(|addr|{
            IViraBase::FeedList {
                addr: *addr,
                min: min_feed,
                max: max_feed,
                balance: U256::ZERO,
                feedAmount: U256::ZERO,
            }
        }).collect();

        //println!("feed_list: {:?}", feed_list);

        // 获取需要转账的数量
        let total_feed_res = base.checkFeed(feed_list.clone()).call().await?;
        let feed_list = total_feed_res._0;
        let total = total_feed_res._1;

        // ------------------- 打印每个地址的详细信息 -------------------
        for (i, f) in feed_list.iter().enumerate() {
            let balance_str = util::format_units_to_3_decimal(f.balance, 18);
            let feed_amount_str = util::format_units_to_3_decimal(f.feedAmount, 18);
            let max_feed_str = util::format_units_to_3_decimal(f.max, 18);

            println!(
                "#{:3} {}  balance: {}, feedAmount: {}, maxFeed: {}",
                i + 1,
                f.addr,
                balance_str,
                feed_amount_str,
                max_feed_str
            );
        }

        if total.is_zero() {
            println!("📌 无需转账");
            return Ok(());
        } else {
            println!("📌 总需要转账数量: {}", utils::format_ether(total));
        }

        // ------------------- 随机挑选一个 signer 地址 -------------------
        let rand_addr = util::choose_one(provider.signer_addresses()).expect("No wallet registered");
        println!("👜 随机选择的钱包地址: {}", rand_addr);

        // ------------------- 发起交易 -------------------
        let nonce = USER.nonce.get_and_increment_nonce(&rand_addr);
        let gas = Gas::new();
        let pending_tx = base
            .feed(feed_list, CONFIG.eth.addr.clone(), total)
            .from(rand_addr)   // 指定用哪个钱包签名
            .chain_id(crate::STATUS.chain_id())
            .gas(2000000)
            .nonce(nonce)
            .gas_price(gas.get_gas_price().to::<u128>())
            .send()
            .await?;

        println!("🚀 交易已发送，hash: {:?}", pending_tx.tx_hash());

        // 等待上链确认
        let receipt = pending_tx.get_receipt().await;
        match receipt {
            Ok(_) => {
                println!("✅ feed成功");
            }
            Err(e) => {
                println!("❌ feed失败: {:?}", e);
            }
        }
        Ok(())

    }

}

mod tests {
    use alloy::primitives::{Address, U256};

    use crate::vira::{contract::ViraData, Vira};

    
    

    #[tokio::test]
    async fn test_batch_check_mev_fallback() {
        // 这个测试用于验证降级机制
        // 注意：这个测试需要真实的合约环境才能运行
        // 在实际环境中，可以通过模拟合约调用失败来测试降级机制

        let vira = Vira::new().await;

        // 创建一些测试数据
        let test_inputs = vec![
            vec![ViraData::PoolReq {
                addr: Address::ZERO,
                version: U256::from(2),
                fee: U256::ZERO,
                fp: U256::ZERO,
                inIndex: U256::ZERO,
                outIndex: U256::from(1),
            }],
            vec![ViraData::PoolReq {
                addr: Address::ZERO,
                version: U256::from(2),
                fee: U256::ZERO,
                fp: U256::ZERO,
                inIndex: U256::from(1),
                outIndex: U256::ZERO,
            }],
        ];

        // 尝试调用batch_check_mev
        // 在真实环境中，如果批量调用失败，应该会自动降级为单个调用
        match vira.contract.batch_check_mev(test_inputs).await {
            Ok(results) => {
                println!("✅ 批量MEV检查成功，结果数量: {}", results.len());
                for (i, result) in results.iter().enumerate() {
                    println!("  结果 {}: gas={}, fee0_len={}, fee1_len={}",
                        i, result.gas, result.fee0.len(), result.fee1.len());
                }
            }
            Err(e) => {
                println!("❌ MEV检查失败: {:?}", e);
                // 在测试环境中，这可能是预期的，因为没有真实的合约环境
            }
        }
    }
}