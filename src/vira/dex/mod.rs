//pub mod bera;
pub mod factory;
pub mod uni_v2;
pub mod uni_v3;

use factory::FactoryConfig;
use serde::{Deserialize, Serialize};

use self::uni_v2::UniV2;
use self::uni_v3::UniV3;



pub trait DexRouter {
    //for config only
    fn new(config : FactoryConfig) -> DEX {
        UniV2::new(config)
    }

    //v2
    //fn getFactory(&self) -> String;
    //fn getPair(&self, token0: &str, token1: &str) -> String;
    //fn weth(&self) -> String;
    fn config(&self) -> &FactoryConfig;
    fn config_mut(&mut self) -> &mut FactoryConfig;

    /*
    async fn load_pools(&self, middleware: &Connector) -> Result<Vec<POOL>, Box<dyn std::error::Error>> {
        Ok(vec![])
    }
     */
}

/*
#[derive(Debug, Clone)]
pub enum DEX {
    UniV1(UniV1),
    Bera(Bera),
}
 */



macro_rules! dex {
    ($($dex_type:ident),+ $(,)?) => {
        #[derive(Debug, Clone, Serialize, Deserialize)]
        pub enum DEX {
            $($dex_type($dex_type),)+
        }

        impl DexRouter for DEX {
            fn config(&self) ->  &FactoryConfig {
                match self {
                    $(DEX::$dex_type(pool) => pool.config(),)+
                }
            }

            fn config_mut(&mut self) ->  &mut FactoryConfig {
                match self {
                    $(DEX::$dex_type(pool) => pool.config_mut(),)+
                }
            }
        }
    };
}
dex!(UniV2, UniV3);