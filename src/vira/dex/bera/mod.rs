use std::u128;
use std::sync::Arc;
use contract::PoolDataOn<PERSON>hain;
use pool::<PERSON>raPool;
use serde::{Deserialize, Serialize};
use crate::{connector::Connector, vira::{pool::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>wa<PERSON><PERSON><PERSON>, <PERSON>O<PERSON>}, util}};

use super::{DexRouter, factory::FactoryConfig, DEX};


pub mod contract;
pub mod pool;


#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Bera {
    pub config : FactoryConfig,
}

impl Bera {
    /*
    async fn batch_get_pool_addr(&self) -> Vec<&str>{
        vec![
            "0xd69adb6fb5fd6d06e6ceec5405d95a37f96e3b96",
            "0x14ee0a8dcd1714781aa0d026f46fc7f77b73c01d",
            "0xd28d852cbcc68dcec922f6d5c7a8185dbaa104b7",
            "0x50f7d4da89f720fbfb35be369f34c6b51e2cada1",
            "0xe8d14085c15ffdb6465d27ef9d292eb953d50928",
            "0xbf3faa5700fd686dcbab8b2b3043b5603530e54f",
            "0xf09386764d08435e209b959c52c3f11872e9a42d",
            "0x0d30be7305e800d4a0dfd8d4d7cacd5123831b4a",
            "0x74734cac7f6578f02fbfe614f0840a3658c6dce6",
            "0x0e4592cf4024aeb7da5f54bc9bf3923a4614de82",
            "0x87787e4bf1208997304908e72e9b71e3b6adf304",
        ]
    }
    
    async fn batch_get_pool_info(&self, middleware: &Connector, pools: Vec<&str>) -> Result<Vec<PoolDataOnChain>, Box<dyn std::error::Error>> {
        let router_addr = self.config.addr.clone();
        let pairs = contract::get_bera_pool_data_batch_request(router_addr, pools, middleware).await?;
        Ok(pairs)
    }
    */
}

impl DexRouter for Bera {
    //TODO: 需要改成balancer，暂时隐藏
    /*
    fn new(config : FactoryConfig) -> DEX {
        let mut c = config.clone();
        c.router_type = 20;
        
        DEX::Bera(Bera {
            config : c
        })
    }
    */

    fn config(&self) -> &FactoryConfig {
        &self.config
    }

    fn config_mut(&mut self) -> &mut FactoryConfig {
        &mut self.config
    }
/*
    async fn load_pools(&self, p: &Connector) -> Result<Vec<POOL>, Box<dyn std::error::Error>> 
    {
        let addrs = self.batch_get_pool_addr().await;
        let mut pairs : Vec<POOL> = vec![];
        
        let pair_data_online = self.batch_get_pool_info(p, addrs).await?;
        
        //把pairs组装成Pair
        pair_data_online.into_iter().for_each(|x| {
            //把x.fee转换成u128
            let fee = x.fee.to::<u128>() / 1e14 as u128;
            
            let mut tokens = vec![];

            for i in 0..x.tokens.len(){
                tokens.push(PoolDataToken {
                    index : i,
                    addr : x.tokens[i],
                    decimal : x.decimals[i].clone(),
                    symbol : x.symbols[i].to_string(),
                    reserve : x.reserves[i].clone(),
                    weight : x.weights[i].clone(),
                    fee : 0,
                });
            }
            //main_pool
            let data = PoolData {
                id : 0,
                addr : x.addr.clone(),
                routers : vec![self.config.addr.clone()],
                ver : 20, //bera
                swap_way : SwapWay::Router,
                tokens,

                fp : fee as u16,
                update_time : 0,

                ..Default::default()
            };
            let bera_pool = BeraPool { data : data};

            //sub pool
            if x.tokens.len() > 2 {
                let mut sub_pools = bera_pool.get_sub_pools();
                pairs.append(&mut sub_pools);
            }
            //main pool
            pairs.push(POOL::BeraPool(bera_pool));
        });
    
        Ok(pairs)
    }
*/
}


#[cfg(test)]
mod tests {

}