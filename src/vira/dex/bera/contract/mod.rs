use std::{ops::Add, sync::Arc};

use alloy::{ dyn_abi::DynSolType, primitives::{ Address, U256},sol};


//use crate::{errors::AMMError, vira::types::PoolDataOnChain};
//use ethers::prelude::abigen;
use std::str::FromStr;
use eyre::Result;

use crate::connector::Connector;

//use ethers::{abi::{ParamType, Token}, providers::Middleware, types::{Bytes, Address},};

#[derive(Debug, Default)]
pub struct PoolDataOnChain {
    pub addr: Address,
    pub weights: Vec<U256>,
    pub symbols: Vec<String>,
    pub tokens: Vec<Address>,
    pub decimals: Vec<u8>,
    pub reserves: Vec<U256>,
    pub fee : U256
}

/*
abigen!(
    IGetBeraPoolDataBatchRequest,
        "src/vira/dex/bera/batch/GetBeraPoolDataBatchRequest.json";
);
 */

// Codegen from ABI file to interact with the contract.
sol!(
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetBeraPoolDataBatchRequest,
    "src/vira/dex/bera/contract/GetBeraPoolDataBatchRequest.json"
);

/*
sol! {
    struct PoolData {
        address addr;
        string[] symbols;
        address[] tokens;
        uint8[] decimals;
        uint256[] reserves;
        uint256 fee;
    }
}
 */

pub async fn get_bera_pool_data_batch_request( router : Address, addrs: Vec<&str>, connector: &Connector,) -> Result<Vec<PoolDataOnChain>>
    {
    //let p = p.clone();
    let target_addresses = addrs.iter().map(|x| Address::from_str(x).unwrap()).collect::<Vec<Address>>();

    let contract_builder = IGetBeraPoolDataBatchRequest::deploy_builder(
        connector.provider(),
        router,
        target_addresses,
    );
    let return_data = contract_builder.call_raw().await?;
    //println!("### return_data: {:?}", return_data);
    //decode the return data
    let pool_type = DynSolType::Array(Box::new(DynSolType::Tuple(vec![
        DynSolType::Address,
        DynSolType::Array(Box::new(DynSolType::Uint(256))),
        DynSolType::Array(Box::new(DynSolType::String)),
        DynSolType::Array(Box::new(DynSolType::Address)),
        DynSolType::Array(Box::new(DynSolType::Uint(8))),
        DynSolType::Array(Box::new(DynSolType::Uint(256))),
        DynSolType::Uint(256),
    ])));
    let decoded = pool_type.abi_decode(&return_data)?;
    let pool_datas : Vec<PoolDataOnChain> = decoded.as_array().unwrap().into_iter().map(|x| {
        let tokens = x.as_tuple().unwrap();
        PoolDataOnChain {
            addr: tokens[0].as_address().unwrap(),
            weights: tokens[1].as_array().unwrap().into_iter().map(|t| t.as_uint().unwrap().0).collect(),
            symbols: tokens[2].as_array().unwrap().into_iter().map(|token| token.as_str().unwrap().to_owned()).collect(),
            tokens: tokens[3].as_array().unwrap().into_iter().map(|token| token.as_address().unwrap()).collect(),
            decimals: tokens[4].as_array().unwrap().into_iter().map(|token| token.as_uint().unwrap().0.to::<u8>()).collect(),
            reserves: tokens[5].as_array().unwrap().into_iter().map(|token| token.as_uint().unwrap().0).collect(),
            fee: tokens[6].as_uint().unwrap().0,
        }
    }).collect();

    //println!("### decoded: {:?}", decoded);


    //print!("pool_datas: {:?}", pool_datas);
    Ok(pool_datas)
}



#[cfg(test)]
mod test {
    use alloy::{network::Ethereum, providers::{Provider, RootProvider}, rpc::{self, client::RpcClient}, transports::BoxTransport};
    use crate::tools::lowc;

    use super::*;

    #[tokio::test]
    pub async fn test_batch_request() -> Result<()> {
        // Set up the HTTP transport which is consumed by the RPC client.
        let rpc_url = "https://bartio.rpc.berachain.com/".parse()?;

        // Create the RPC client.
        let rpc_client = RpcClient::new_http(rpc_url).boxed();
        let provider = RootProvider::<BoxTransport, Ethereum>::new(rpc_client).boxed();
        let latest_block = provider.get_block_number().await?;
        
        let connector = Connector::new(provider);

        // Get latest block number.

        println!("Latest block number: {latest_block}");

        let pools = get_bera_pool_data_batch_request(
            Address::from_str("******************************************")?,
            vec!["******************************************", "******************************************"],
            Arc::new(connector)
        ).await?;
        println!("{:?}", pools);
        Ok(())
    }
}