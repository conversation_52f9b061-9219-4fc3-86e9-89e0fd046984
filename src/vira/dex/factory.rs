use std::str::FromStr;

use alloy::{eips::BlockId, primitives::{Address, B256, U256}, sol_types::SolEvent};

use crate::{connector::Connector, vira::{errors::{DEXError, EventLogError}, pool::POOL}};
use serde::{Deserialize, Serialize};

use super::{
    uni_v2::factory::{IUniswapV2Factory, UniV2Factory},
    uni_v3::factory::{IUniswapV3Factory, UniV3Factory},
};

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FactoryConfig {
    pub name: String,
    pub addr : Address,
    pub eth: Address,     // router指定的eth
    //默认值是1
    pub router_type: u8, // router类型  1)v1 2)v2 3)v3 4)v4 5) 其他根据各个平台自定义
    
    pub fee: U256,          // pool fee
    pub stable_fee: U256,   // 用于v2StableRouter
    pub volatile_fee: U256, // 用于v2StableRouter

    //用于uniswao v3 v4 balancer等
    pub creation_block: u64, 
    //pub synced_block: u64,
    //用于uniswao v2
    //pub indexed: u64,

    pub pools : Vec<POOL>, //TODO: del
}

impl Default for FactoryConfig {
    fn default() -> Self {
        FactoryConfig {
            name: String::default(),
            addr : Address::default(),
            eth: Address::default(),
            router_type: 2,
            fee: U256::from(9970),
            stable_fee: U256::ZERO,
            volatile_fee: U256::ZERO,
            creation_block : 0,
            //synced_block : 0,
            //indexed : 0,
            pools: vec![],
        }
    }
}

impl FactoryConfig {
    pub fn new() -> Self {
        FactoryConfig {
            name: "unknown".to_string(),
            ..Default::default()
        }
    }
    pub fn name(mut self, name: &str) -> Self {
        self.name = name.to_string();
        self
    }
    pub fn addr(mut self, addr: &str) -> Self {
        self.addr = Address::from_str(addr).unwrap();
        self
    }
    pub fn eth(mut self, eth: &str) -> Self {
        self.eth = Address::from_str(eth).unwrap();
        self
    }
    pub fn fee(mut self, fee: u16) -> Self {
        self.fee = U256::from(fee);
        self
    }
    pub fn stable_fee(mut self, fee: u16) -> Self {
        self.stable_fee = U256::from(fee);
        self
    }
    pub fn volatile_fee(mut self, fee: u16) -> Self {
        self.volatile_fee = U256::from(fee);
        self
    }
}

pub trait DexFactory {
    fn new(config: FactoryConfig) -> FACTORY {
        UniV2Factory::new(config)
    }

    fn data(&self) -> &FactoryConfig;
    fn data_mut(&mut self) -> &mut FactoryConfig;

    fn pools(&self) -> &Vec<POOL> {
        &self.data().pools
    }
    fn pools_mut(&mut self) -> &mut Vec<POOL> {
        &mut self.data_mut().pools
    }

    //amms-rs begin
    fn address(&self) -> Address {
        self.data().addr
    }

    /// Returns the block number at which the factory was created.
    fn creation_block(&self) -> u64 {
        self.data().creation_block
    }

    /// Returns the creation event signature for the factory.
    fn pool_creation_event(&self) -> B256;


    //更新pool的数据，常用于更新reserve
    async fn sync(&self, _pools: Vec<POOL>, _to_block: BlockId, _connector: &Connector) -> Result<Vec<POOL>, DEXError> {
        Ok(vec![])
    }

    async fn discover(&mut self, _to_block: BlockId, _connector: &Connector) -> Result<Vec<POOL>, DEXError> {
        Ok(vec![])
    }

    //amms-rs en



    //增量更新pools，主要用于v2
    async fn update_pools(&mut self, _connector: &Connector) -> Result<Vec<Address>, DEXError> {
        Ok(vec![])
    }


}

macro_rules! factory {
    ($($factory_type:ident),+ $(,)?) => {
        #[derive(Debug, Clone, Serialize, Deserialize)]
        pub enum FACTORY {
            $($factory_type($factory_type),)+
        }

        impl DexFactory for FACTORY {
            fn address(&self) -> Address {
                match self {
                    $(FACTORY::$factory_type(f) => f.data.addr,)+
                }
            }

            fn data(&self) ->  &FactoryConfig {
                match self {
                    $(FACTORY::$factory_type(f) => &f.data,)+
                }
            }

            fn data_mut(&mut self) ->  &mut FactoryConfig {
                match self {
                    $(FACTORY::$factory_type(f) => &mut f.data,)+
                }
            }

            fn pools(&self) ->  &Vec<POOL> {
                match self {
                    $(FACTORY::$factory_type(f) => &f.data.pools,)+
                }
            }

            fn pools_mut(&mut self) ->  &mut Vec<POOL> {
                match self {
                    $(FACTORY::$factory_type(f) => &mut f.data_mut().pools,)+
                }
            }

            fn pool_creation_event(&self) -> B256 {
                match self {
                    $(FACTORY::$factory_type(f) => f.pool_creation_event(),)+
                }
            }

            async fn sync(&self, pools: Vec<POOL>, to_block: BlockId, connector: &Connector) -> Result<Vec<POOL>, DEXError> {
                match self {
                    $(FACTORY::$factory_type(factory) => factory.sync(pools, to_block, connector).await,)+
                }
            }

            async fn discover(&mut self, to_block: BlockId, connector: &Connector) -> Result<Vec<POOL>, DEXError> {
                match self {
                    $(FACTORY::$factory_type(factory) => factory.discover(to_block, connector).await,)+
                }
            }

            async fn update_pools(&mut self, connector: &Connector) -> Result<Vec<Address>, DEXError> {
                match self {
                    $(FACTORY::$factory_type(f) => f.update_pools(connector).await,)+
                }
            }
            
        }
    }
}
factory!(UniV2Factory, UniV3Factory);


impl TryFrom<B256> for FACTORY {
    type Error = EventLogError;

    fn try_from(value: B256) -> Result<Self, Self::Error> {
        if value == IUniswapV2Factory::PairCreated::SIGNATURE_HASH {
            Ok(FACTORY::UniV2Factory(UniV2Factory::default()))
        } else if value == IUniswapV3Factory::PoolCreated::SIGNATURE_HASH {
            Ok(FACTORY::UniV3Factory(UniV3Factory::default()))
        } else {
            return Err(EventLogError::InvalidEventSignature);
        }
    }
}