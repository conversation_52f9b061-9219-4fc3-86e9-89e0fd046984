
use alloy::{primitives::B256, sol, sol_types::SolEvent};
use serde::{Deserialize, Serialize};

use crate::vira::dex::factory::{DexFactory, FactoryConfig, FACTORY};

sol! {
    /// Interface of the UniswapV3Factory contract
    #[derive(Debug, PartialEq, Eq)]
    #[sol(rpc)]
    contract IUniswapV3Factory {
        event PoolCreated(address indexed token0, address indexed token1, uint24 indexed fee, int24 tickSpacing, address pool);
        function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool);
        function parameters() returns (address, address, uint24, int24);
        function feeAmountTickSpacing(uint24) returns (int24);
    }
}

#[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct UniV3Factory {
    pub data : FactoryConfig,
}

impl DexFactory for UniV3Factory {
    fn new(config: FactoryConfig) -> FACTORY {
        FACTORY::UniV3Factory(UniV3Factory {
            data: config,
            ..Default::default()
        })
    }

    fn data(&self) -> &FactoryConfig {
        &self.data
    }

    fn data_mut(&mut self) -> &mut FactoryConfig {
        &mut self.data
    }


    fn pool_creation_event(&self) -> B256 {
        IUniswapV3Factory::PoolCreated::SIGNATURE_HASH
    }

}