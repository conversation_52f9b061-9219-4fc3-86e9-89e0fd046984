
use serde::{Deserialize, Serialize};

use crate::{connector::Connector, impl_pool_display, vira::{errors::DEXError, pool::{DexPool, PoolData, POOL}}};

#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct UniV3Pool {
    pub data : PoolData,
}

// 使用宏实现Display特性
impl_pool_display!(UniV3Pool);

impl UniV3Pool {
    pub async fn sync_reserves(_pools : &mut [POOL], _connector : &Connector) -> Result<(), DEXError> {
        //TODO: 实现
        Ok(())
    }
}

//main pool && virtual pool
impl DexPool for UniV3Pool {
    fn data(&self) -> &PoolData {
        &self.data
    }

    fn data_mut(&mut self) -> &mut PoolData {
        &mut self.data
    }
}