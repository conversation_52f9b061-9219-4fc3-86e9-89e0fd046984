use std::collections::HashMap;
use dashmap::DashMap;
use serde::{Serialize, Deserialize};
use alloy::primitives::{utils::{format_units, parse_units}, Address, U256};

#[derive(Default, Serialize, Deserialize, Debug)]
pub struct Token {
    pub addr : Address,
    pub symbol : String,
    pub decimals : u8,

    pub price : f32,
    pub is_eth : bool,   
}

impl Token {
    //根据数量计算价值
    pub fn usd(&self, num : U256) -> f32 {
        let v = format_units(num, self.decimals).unwrap();
        v.parse::<f32>().unwrap() * self.price //可能会溢出, 不过用于计价的token价格数值都不会太极端，暂无隐患
    }

    pub fn to_usd(amount: U256, decimals: u8, price: f32) -> f32 {
        let v = format_units(amount, decimals).unwrap();
        v.parse::<f32>().unwrap() * price
    }

    pub fn to_amount(usd: f32, decimals: u8, price: f32) -> U256 {
        let amount = usd / price;
        //取小数点后的decimals转换成string
        let amount_str = format!("{:.decimals$}", amount, decimals = decimals as usize);
        parse_units(&amount_str, decimals).expect("to_amount error").into()
    }
}

#[derive(Default, Serialize, Deserialize)]
pub struct TokenManager {
    pub data : DashMap<Address, Token>,
}

impl TokenManager {
    pub fn new() -> TokenManager {
        TokenManager {
            ..Default::default()
        }
    }

    pub fn add(&self, token: Token) {
        //if !self.data.contains_key(&token.addr) {
            self.data.insert(token.addr, token);
        //}
    }
}