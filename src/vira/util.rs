use std::fmt::Display;

use alloy::primitives::U256;

use crate::vira::consts::U_10;

//所有一对的组合
pub fn all_sets<T>(arr : &Vec<T>) -> Vec<Vec<T>>
    where T : Clone
{
    let mut result = vec![];
    let len = arr.len();
    for i in 0..len {
        for j in i+1..len {
            result.push(vec![arr[i].clone(), arr[j].clone()]);
        }
    }  
    result
}

pub fn to_percent<T: Into<f64>>(numerator: T, denominator: T) -> f64 {
    let a_f64: f64 = numerator.into();
    let b_f64: f64 = denominator.into();
    a_f64 / b_f64 * 100.0
}

pub fn display_vec<T: Display >(vec: &Vec<T>) {
    for v in vec {
        println!("{}", v);
    }
}

pub async fn delay(ms: u64) {
    if ms > 0 {
        tokio::time::sleep(tokio::time::Duration::from_millis(ms)).await;
    }
}

//精度转换，把U256 A精度的数量转换成等值的精度B的数量，如果精度相同则直接返回amount
pub fn decimals_convert(amount : U256, from : u8, to : u8) -> U256 {
    if from == to || amount.is_zero(){
        return amount;
    }
    if from > to {
        amount /  U_10.pow(U256::from(from - to))
    } else {
        amount * U_10.pow(U256::from(to - from))
    }
}

//把amount相同价值的from token数量，转换成to token的数量
pub fn value_convert(amount : U256, from_decimals : u8, to_decimals : u8, from_price:f32, to_price:f32) -> U256 {
    if from_decimals == to_decimals && from_price == to_price {
        return amount;
    }

    U256::ZERO
}


/// 随机生成 [0, n-1] 的 usize
pub fn rand_usize(n: usize) -> usize {
    fastrand::usize(0..n)
}

pub fn rand_elem<T>(slice: &[T]) -> Option<&T> {
    if slice.is_empty() {
        return None;
    }
    let index = fastrand::usize(0..slice.len());
    Some(&slice[index])
}

pub fn choose_one<I, T>(mut iter: I) -> Option<T>
where
    I: Iterator<Item = T>,
{
    let mut selected = None;
    let mut count = 0;

    for item in iter {
        count += 1;
        // 以 1/count 的概率保留当前元素
        if fastrand::usize(..count) == 0 {
            selected = Some(item);
        }
    }

    selected
}


/// 格式化U256数值为指定小数位数的字符串，保留3位小数
/// 
/// # 参数
/// * `value` - 需要格式化的U256数值
/// * `decimals` - 代币的小数位数
/// 
/// # 返回值
/// * `String` - 格式化后的字符串
/// 
/// # 优化说明
/// 1. 添加输入验证
/// 2. 改进错误处理
/// 3. 添加详细注释
pub fn format_units_to_3_decimal(value: U256, decimals: u8) -> String {
    // 调用alloy库的format_units函数进行基本格式化
    match alloy::primitives::utils::format_units(value, decimals) {
        Ok(formatted_string) => {
            // 将格式化后的字符串解析为f64数值
            match formatted_string.parse::<f64>() {
                Ok(numeric_value) => {
                    // 格式化为保留3位小数的字符串
                    format!("{:.3}", numeric_value)
                },
                Err(parse_error) => {
                    // 解析失败时记录错误并返回默认值
                    eprintln!("数值解析错误: {}", parse_error);
                    "0.000".to_string()
                }
            }
        },
        Err(format_error) => {
            // 格式化失败时记录错误并返回默认值
            eprintln!("单位格式化错误: {}", format_error);
            "0.000".to_string()
        }
    }
}

#[cfg(test)]
mod tests {
    pub use super::*;
    use alloy::primitives::U256;

    #[test]
    pub fn test_all_sets(){
        let arr = vec![1,2,3];
        let result = all_sets(&arr);
        println!("result: {:?}", result);
    }

    #[test]
    pub fn test_decimal_convert_same_precision() {
        let amount = U256::from(1000);
        let result = decimals_convert(amount, 18, 18);
        assert_eq!(result, amount);
    }

    #[test]
    pub fn test_decimal_convert_higher_to_lower_precision() {
        let amount = U256::from(1000000);
        let result = decimals_convert(amount, 6, 3);
        assert_eq!(result, U256::from(1000));
    }

    #[test]
    pub fn test_decimal_convert_lower_to_higher_precision() {
        let amount = U256::from(1000);
        let result = decimals_convert(amount, 3, 6);
        assert_eq!(result, U256::from(1000000));
    }

    #[test]
    pub fn test_decimal_convert_zero_amount() {
        let amount = U256::from(0);
        let result = decimals_convert(amount, 18, 6);
        assert_eq!(result, U256::from(0));
    }

    #[test]
    pub fn test_decimal_convert_large_numbers() {
        let amount = U256::from(1_000_000_000_000_000_000u128); // 1e18
        let result = decimals_convert(amount, 18, 6);
        assert_eq!(result, U256::from(1_000_000));
    }
}