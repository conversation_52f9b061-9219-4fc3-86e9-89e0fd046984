use bera::master::server;
use eyre::Result;

/// 服务器管理器主程序
/// 
/// 这个程序提供了与TypeScript版本server.ts相同的功能，用于管理多个远程服务器上的bot部署。
/// 
/// 使用方法:
/// ```bash
/// # 列出所有Google Cloud主机的PM2状态
/// cargo run --bin server_manager ls
/// 
/// # 从主服务器下载数据
/// cargo run --bin server_manager OEC d
/// 
/// # 从主服务器下载路由配置
/// cargo run --bin server_manager OEC d_router
/// 
/// # 上传bot到主服务器
/// cargo run --bin server_manager OEC u_bot
/// 
/// # 上传数据到主服务器
/// cargo run --bin server_manager OEC u_data
/// 
/// # 上传路由配置到主服务器
/// cargo run --bin server_manager OEC u_router
/// 
/// # 上传bot到所有从服务器并重启
/// cargo run --bin server_manager OEC u_all_bot
/// 
/// # 上传路由配置到所有从服务器并重启
/// cargo run --bin server_manager OEC u_all_router
/// 
/// # 上传所有文件到从服务器并重启
/// cargo run --bin server_manager OEC u_all
/// 
/// # 测试操作（重启主服务器上的进程）
/// cargo run --bin server_manager OEC test
/// 
/// # 停止所有服务器上的进程
/// cargo run --bin server_manager OEC stop_all
/// ```
/// 
/// 支持的区块链:
/// OEC, REI, KAI, KUB, HECO, KCC, ASTR, KLAY, GLMR, MOVR, XDAI, POLYGON, 
/// CELO, ETHW, BSC, ONE, MILKADA, SAMA, POM, AURORA, LAT, VS, ARB, METIS, 
/// CORE, PLS, KAVA, BASE, OPBNB, WEMIX, ELA, ZETA, OP, MXC, DEGEN, BEVM, 
/// SEI, S, ABS, X
#[tokio::main]
async fn main() -> Result<()> {
    let chain = "x";
    // 运行服务器管理器
    //run_server_manager().await?;
    let host = server::get_chain_hosts(chain);

    let main_host = &host[0];

    main_host.upload_bot(chain)?;
    //main_host.upload_router(chain)?;

    Ok(())
}
