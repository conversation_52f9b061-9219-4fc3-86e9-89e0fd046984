import bot from "../bot";
import BotTrash from "./botPumpTrash";
import DataType, { FindPathResult, MevPath, MixGas, ServerPumpData } from "./type/DataType";
import { macro } from "./macro";
import { BigNumber, ethers } from "ethers";
import tools from "./tools";
import Lazy from "./lazy/LazyController";
import ExTimer from "./comp/ExTimer";
import MevDetails from "./comp/MevDetails";

class MevPathWithVal {
    mev : MevPath = new MevPath();
    sum = 0;
    vals : RewardLog[] = [];
}

class RewardLog{
    ts = 0;
    val = 0;
}

export default class BotJunk extends BotTrash {

    details = new MevDetails(30);

    junkRate = 1.5;   //from 1.5s ~10s
    tick = 0; //5分钟一个tick

    constructor(wallets:{[k:string]:string}, addr:string){
        super(wallets, addr);
    }


    async init(){
        while(true){
            if(bot.oracleV2?.isReady){
                break;
            }
            await tools.delay(3000);
        }
        console.log("[junk init]");

        let topMevs = this.findBestMevPaths();
        topMevs.forEach(m=> this.addMevRecord(m, 0.1));
        this.generateTopMevs();

        this.repeatDoJunk(bot.config.junk.delay_ms);

        //前30分钟每分钟检查一次
        let keepActiveTick = 20;
        for(let i = 0 ; i < keepActiveTick; i++){
            setTimeout(() => {
                this.generateTopMevs();
                if(i == keepActiveTick - 1){
                    setInterval(() => {
                        this.generateTopMevs();
                    }, 1000 * 60 * 3.5); //5分钟一次
                }
            }, 1000 * 60 * (i+1));
        }
    }


    addMevRecord(m : MevPath, reward:number){
        this.details.addMevRecord(m, reward);
        if(reward > 20){
            ExTimer.setTimeOut("generateTopMevs", ()=>{
                this.generateTopMevs();
            }, 10 * 1000);
        }
    }

    generateTopMevs(){
        this.details.generateTopMevs();

        // 计算前10个MevPathWithVal对象的sum属性总和
        let topMevs = this.details.topMevsPathKey.map(k => this.details.mevMap[k]);
        const top10Sum = topMevs.slice(0, 10).reduce((total, item) => total + item.sum, 0);

        if(top10Sum < 100){
            this.junkRate = 60;
        } else if(top10Sum < 200){
            this.junkRate = 20;
        } else if(top10Sum < 300){
            this.junkRate = 5;
        } else if(top10Sum < 450){
            this.junkRate = 3;
        } else if(top10Sum < 700){
            this.junkRate = 2;
        } else if(top10Sum < 1000){
            this.junkRate = 1.3;
        } else {
            this.junkRate = 1;
        }

        this.junkRate = Math.min(this.junkRate, bot.config.junk.delay_ms / 1000);


        console.log(`${macro.COLOR.BBlack}uptime: ${this.details.formatUpTime()} junk_rate : ${this.junkRate} s, all_time : $${this.details.total_reward.toFixed(3)}, 30m sum: $${top10Sum.toFixed(3)}`);
        //打印平均每小时的cost和reward
        let costHour = this.details.total_cost * (3600 / this.details.uptime);
        let rewardHour = this.details.latest_reward * (3600 / this.details.uptime);
        this.details.displayTopMevs(topMevs, false);
        console.log(`${macro.COLOR.BBlack} avg cost: $${costHour.toFixed(3)} / h, avg reward: $${rewardHour.toFixed(3)} / h`);
        this.tick +=1;

    }

    //找出所有mev中价值最大的n条mevPath
    findBestMevPaths(len = bot.config.junk.maxPair, display = false){
        let bestMev : MevPath[] = [];
        const mevPaths: MevPathWithVal[] = [];
        const pathKeys = new Set<string>(); // 用于跟踪已处理的路径
        
        // 遍历所有pair，收集mev路径并计算其价值
        bot.oracleV2.data.pm.data.forEach((pair, address)=>{            
            pair.mevPath.forEach((mev)=>{
                // 为当前路径创建标准化键
                const pathKey = this.details.normalizePathKey(mev.pairs);
                // 如果此路径（或其反向路径）尚未处理
                if (!pathKeys.has(pathKey)) {
                    pathKeys.add(pathKey);
                    
                    // 计算MEV路径的价值
                    const token = bot.token(mev.s0);
                    let val = bot.oracleV2.data.calcLowestValue(mev.s0, mev.pairs).lowest;
                    mevPaths.push({ mev, sum:val, vals:[] });
                }
            });
        });

        // 按价值降序排序
        mevPaths.sort((a, b) => b.sum - a.sum);
        
        const topMevs = mevPaths.slice(0, len);
        bestMev = topMevs.map(item => item.mev);
        
        if(display) this.details.displayTopMevs(topMevs, true);

        console.log(`[findBestMevPaths] ${bestMev.length}`);
        return bestMev;
    }


    repeatDoJunk(timeout : number){
        setTimeout(() => {
            this.doJunk();
            let delay = 0;
            if (this.details.uptime <= 60 * 20 ){
                delay = bot.config.junk.delay_ms;
            } else {
                delay = this.junkRate * 1000;
            }
            this.repeatDoJunk(delay);
        }, timeout);
    }

    doJunk(){ 
        if(this.details.topMevsPathKey.length == 0) return;
        //根据 bestMevs生成FindPathResult[]
        let r : FindPathResult[] = [];
        let mixGas = new MixGas();

        let maxPath = 2;
        
        //统计ton10当中sum大于70的数量
        let count = 0;
        this.details.topMevsPathKey.forEach(k => {
            if(this.details.mevMap[k].sum > 50) count++;
        });
        if(count >= 4) {
            maxPath = 4;
        } else if(count >= 3) {
            maxPath = 3;
        } else if(count >= 2) {
            maxPath = 2;
        }
        /*
        else if(count >= 1){
            maxPath = 1;
        } else {
            return;
        }
        */
        maxPath = Math.min(bot.config.junk.maxPair, maxPath);
        
        
        let bestMevs = this.details.topMevsPathKey.map(k => this.details.mevMap[k].mev).slice(0, maxPath);

        bestMevs.forEach((mev)=>{
            let cost = mixGas.setGasLimit(BigNumber.from(mev.gas)).getGasCost(mev.s0);
            //正序
            r.push({
                convertEth : mev.convertEth,
                gas : mev.gas,
                fees : mev.fees0,
                mevType : mev.type,
                tokenIn0or1 : mev.tokenIn0or1[0],

                pairs : mev.pairs,
                amount : macro.bn.zero,
                reward : 0,
                rewardValue : 0,
                
                stable : mev.s0,
                gasCost : cost.bn,

                maxFeePerGas: macro.bn.zero,
                gasLimit : macro.bn.zero,

                amounts: [],
                tokenOuts : []
            });
            //逆序
            let rPair = [...mev.pairs];
            rPair.reverse();
            r.push({
                convertEth : mev.convertEth,
                gas : mev.gas,
                fees : mev.fees1,
                mevType : mev.type,
                tokenIn0or1 : mev.tokenIn0or1[1],

                pairs : rPair,
                amount : macro.bn.zero,
                reward : 0,
                rewardValue : 0,
                
                stable : mev.s1,
                gasCost : cost.bn,

                maxFeePerGas: macro.bn.zero,
                gasLimit : macro.bn.zero,

                amounts: [],
                tokenOuts : []
            });
        });
        //console.log(`${macro.COLOR.Blue}   Junk begin: pairs (${r.length})${macro.COLOR.Off}`)
        this.batchJunk(r, mixGas, 0, false, undefined);
        return r;
    }

    async batchJunk(paths:FindPathResult[], mixGas:MixGas, opIndex:number, bid=false, customNonce?:number){
        let filteredPaths : FindPathResult[] = paths;

        let s = DataType.findPathResultsToServerPumpData(filteredPaths, "0x1e18d80d");
        //let s = DataType.findPathResultsToServerPumpData(filteredPaths);
        Lazy.ins().logTs(`${macro.COLOR.Purple}[junk] ${mixGas.display()} len:${filteredPaths.length}/${paths.length}${macro.COLOR.Off}`);
        const opAddr = this.operators[opIndex].ins().address;

        const opNonce = bot.handle.nonce.get(opAddr);
        this.pumpTrash(s, mixGas, opIndex, s.totalRewardValue, customNonce);
    }


    async logTrash(receipt:ethers.providers.TransactionReceipt, serverData:ServerPumpData[], seed:number, expectRewards:number){
        if(!this.hashPool.enable(receipt.transactionHash)) return;

        //计算收益
        const { effectiveGasPrice, cumulativeGasUsed, gasUsed } = receipt;
        const bEth = bot.token(bot.config.eth);
        const costVal = bEth.toNum((effectiveGasPrice || cumulativeGasUsed).mul(gasUsed)) * bEth.price;

        const sum = {
            hash : receipt.transactionHash,
            block : receipt.blockNumber,
            index : receipt.transactionIndex,
            total : 0,
            success : 0,
            reward : 0
        };
        let results : number[] = [];
        let rewards : BigNumber[];

        const logs = (receipt as any)["logs"] as Array<any>;
        if(logs && logs.length > 0){
            let logData = logs[logs.length-1].data;
            [ results, rewards ]= bot.abiCoder.decode(["uint8[]","uint112[]"], logData);
            sum.total = results.length;
            for(let i = 0; i < serverData.length; i++){
                if(results[i] == macro.PUMP_RESULTS.Success || results[i] == macro.PUMP_RESULTS.SuccessOverlow){
                    sum.success++;
                    const pIn = bot.oracleV2.data.pm.get(serverData[i].pairs[0].addr);
                    const tokenIn = bot.token(serverData[i].tokenIn0or1.isZero() ? pIn.token0 : pIn.token1);
                    sum.reward += tokenIn.toNum(rewards[i]) * tokenIn.price;
                }
            }
        }

    
        let rewardStr = "";

        let str : string[] = [];
        if(sum.reward > 0){
            rewardStr = `$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} ${results.join(",")} `;
            str.push(`$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)}`);
            str.push(sum.index.toString());
        } else {
            rewardStr = `$ - ${costVal.toFixed(4)} ${macro.COLOR.BBlack}${results.join(",")}`;
            str.push(`$ - ${costVal.toFixed(4)})`);
            str.push(sum.index.toString());
        }
        this.details.total_cost += Number(costVal.toFixed(4));
        this.details.latest_reward += sum.reward;
        if(sum.reward > 0){
            bot.localLog.append(macro.FILE.LOG_JUNK, sum.hash, str);
        }
        Lazy.ins().log(`${macro.COLOR.Purple}junk: ${sum.hash} ${sum.block} (${sum.index}) `
                +`${sum.reward > 0 ? macro.COLOR.BPurple : ""}${rewardStr} ${macro.COLOR.BBlack} [st: ${this.details.latest_reward.toFixed(3)} - ${this.details.total_cost.toFixed(3)} = $ ${(this.details.latest_reward - this.details.total_cost).toFixed(3)}]${macro.COLOR.Off}`);
    }

}