import bot from "../bot";
import { ethers, utils } from "ethers";
import BotBear from "./botBear";
import BotBull from "./botBull";
import { macro } from "./macro";
import BotPump from "./botPump";
import BotTrash from "./botPumpTrash";
import <PERSON><PERSON> from "./lazy/LazyController";
import BotEoaRouter from "./botPumpEoaRouter";
import BotPumpSafe from "./botPumpSafe";
import { StableBalanceSum } from "./type/DataType";
import ExTimer from "./comp/ExTimer";
import BotJunk from "./botPumpJunk";
export default class Client {

    //public walletBuyer : ethers.Wallet;
    public iBot = new ethers.utils.Interface(macro.abi.bot);
    public iRouter = new ethers.utils.Interface(macro.abi.router);

    public bull : BotBull;
    public bear : BotBear;
    public pump : BotPump;
    public trash : BotTrash;
    public junk : BotJunk;
    public safe : BotPumpSafe;

    public oneRouter : BotEoaRouter = new BotEoaRouter();

    //public oracleOperator : ethers.Wallet;
    //public oracles : Map<string, Oracle> = new Map();

    public stableBalance = new StableBalanceSum();


    constructor(){
        const {config, walletData} = bot;

        //for(const r of Object.keys(config.routers)){
        //    this.oracles.set(r, new Oracle(r));
        //}

        //初始化牛牛
        this.bull = new BotBull(walletData.bull, config.bot.address);
        //初始化熊熊
        this.bear = new BotBear(walletData.bear, config.bot2.address);
        //初始化泵泵
        this.pump = new BotPump(walletData.pump, config.bot.address);
        //初始化残渣机器人
        this.trash = new BotTrash(walletData.bull, config.bot.address);
        this.safe = new BotPumpSafe(walletData.bull, config.bot.address);
        //垃圾机器人
        this.junk = new BotJunk(walletData.bear, config.bot.address);

        if(bot.mode !== macro.BOT_MODE.LOCAL && bot.mode !== macro.BOT_MODE.PAIR && bot.mode !== macro.BOT_MODE.UPDATE  && bot.mode !== macro.BOT_MODE.TEST && bot.mode !== macro.BOT_MODE.MEV){
            if(!bot.config.bot.active){
                setTimeout(() => {this.updateStableBalance();}, 2000);
                setInterval(()=>{this.updateStableBalance();}, 1200 * bot.config.blockTime * 10);
                //新增自动更新pair
            }
            if(bot.config.eoaRouter.active) this.oneRouter.init();
        }
        
        ExTimer.setIntervalOld(()=>{ this.stableBalance.enableLogSum = true; }, 1000 * 300);
    }

    private async updateStableBalance(){
        this.stableBalance.show();
    }

    private async updateV2Router(){
        bot.oracleV2.data.updateCachePairs(true);
    }

}