import { BigNumber, ethers, utils } from "ethers";
import bot from "../bot";
import tools from "./tools";
import HashPool from "./comp/HashPool";
import Lazy from "./lazy/LazyController";
import ProviderNonceManager from "./provider/ProviderNonceManager";
import { ProviderWs } from "./provider/ProviderWs";
import { MixGas, SwapResult } from "./type/DataType";
import { macro, UniswapV2Func } from "./macro";
import { ProviderHttp } from "./provider/ProviderHttp";
import { ProviderWsDoge } from "./provider/ProviderWsDoge";
import { ProviderHttpTxPool } from "./provider/ProviderHttpTxPool";
import { ProviderWsCanto } from "./provider/ProviderWsCanto";
import { ProviderWsSafe } from "./provider/ProviderWsSafe";
import { PairExtend } from "./type/Pair";
import AutoGasPrice from "./comp/AutoGasPrice";
import { ProviderWsX } from "./provider/ProviderWsX";

export default class DataHandle {
    wss : Array<ProviderWs|ProviderHttp|ProviderWsDoge> = [];
    isListeningPending = false;
    blockNum = 0;
    blockBeginTime = 0;

    highestGas : BigNumber = BigNumber.from('0');
    highestMixGas : MixGas = new MixGas();

    gasTrackEnable = false;
    blackListCache : string[] = [];

    nonce = new ProviderNonceManager(bot.config.blockTime * 2);
    hashPool = new HashPool(300);

    reportFirstTx = false;

    block = {
        type : 0,
        chainId : 0,
        isEIP1559 : false,
    }

    l1GasPrice? : AutoGasPrice;

    constructor(){
        //忽略本机的provider
        if(bot.mode == macro.BOT_MODE.LOCAL){
            bot.config.mainnet.wss = bot.config.mainnet.wss.filter(x=> !x.includes('127'));
            console.log(bot.config.mainnet.wss);
            if(bot.config.mainnet.wss.length == 0) bot.config.mainnet.wss = [bot.config.mainnet.data];
        }
        const {wss} = bot.config.mainnet;
        for(let i = 0; i < wss.length; i++){
            const args = wss[i].split('|');
            const url = args.pop() || "";
            let onlyBroadcast = args.includes("b");
            console.log(args, url);
            if(url.includes('http')){
                if(args.includes('txpool')){
                    this.wss.push(new ProviderHttpTxPool(url, i)); 
                } else if(args.includes('txpoolOnly')){
                    this.wss.push(new ProviderHttpTxPool(url, i, true));
                } else {
                    this.wss.push(new ProviderHttp(url, i, onlyBroadcast));
                }
            } else {
                if (args.includes("doge")){
                    this.wss.push(new ProviderWsDoge(url, i));
                } else if(args.includes("canto")){
                    this.wss.push(new ProviderWsCanto(url, i));
                } else if(args.includes("safe")){
                    this.wss.push(new ProviderWsSafe(url, i));
                } else if(args.includes("x")){
                    this.wss.push(new ProviderWsX(url, i));
                } else {
                    this.wss.push(new ProviderWs(url, i, onlyBroadcast));
                }
            }
        }
        for(let v of Object.values(bot.walletData)){
            for(let address of Object.keys(v)){
                this.nonce.set(address);
            }
        }
        this.isListeningPending = bot.config.bot.active || bot.config.bot2.active || bot.config.pump.active;
        this.init();

        if(bot.config.trash.l2 && bot.config.trash.l2.l1GasRpc) this.l1GasPrice = new AutoGasPrice(bot.config.trash.l2.l1GasRpc, 120)
    }
    //获取当前gas最小值
    minGas(){
        let minGas = ethers.utils.parseUnits(bot.config.gasMin.toString(), 'gwei');
        //console.log("minGas1: ", utils.formatUnits(minGas, 'gwei'));
        this.wss.forEach((w)=>{
            if(!w.onlyBroadcast && w.minGas.gt(minGas)) minGas = w.minGas;
        });
        //console.log("minGas2: ", utils.formatUnits(minGas, 'gwei'));
        return minGas;
    }

    async init(){
        while(!this.wss[0].isReady){
            await tools.delay(2000);
        }
        //bot.event.on(macro.EVENT.WS_ON_PENDING, this.onPending.bind(this));
        bot.event.on(macro.EVENT.WS_ON_BLOCK, this.onBlock.bind(this));
        this.block.chainId = (await this.wss[0].provider.getNetwork()).chainId;
        const feeData = await this.wss[0].provider.getFeeData();
        if (feeData.maxFeePerGas != null && feeData.maxPriorityFeePerGas != null) {
            // The network supports EIP-1559!
            this.block.type = 2;
            this.block.isEIP1559 = true;
        } else if (feeData.gasPrice != null) {
            this.block.type = 0;
        }
        console.log(this.block);
        this.nonce.init();
        //console.log(this.wss[0]);
    }

    async getTransactionCountBatch(address:string){
        /*
        const promises : Array<Promise<number>> = [];
        this.wss.forEach((p)=>{
            if(!p.onlyBroadcast) promises.push(p.provider.getTransactionCount(address));
        });
        return await Promise.any(promises);
        */
        return await bot.provider().getTransactionCount(address);
    }
    
    onPending(trans:ethers.providers.TransactionResponse, websocketId:number){
        //if(trans.to == "******************************************") console.log( " onPending: ", trans);
        //定时上报first tx
        if(!bot.client) return;
        if(this.reportFirstTx){
            Lazy.ins().logFirstTx(this.blockNum, trans.hash);
            this.reportFirstTx = false;
        }
    
        this.gasFilter(trans, websocketId);
        const to = trans.to || macro.zeroAddress;
        if(bot.config.blackList.includes(to) || bot.blackList.has(to)) return; //忽略垃圾机器人

        const data = trans.data;
        let selector = macro.uniswapV2FuncMap[data.slice(2,10)];
        //console.log(to, bot.oracleV2.isActiveRouter(to));
        if(!selector){
            return;
        } else if(bot.oracleV2.isActiveRouter(to)){
            bot.routers.get(to).decodeData(trans, selector, to, websocketId, this.onSwaps.bind(this), this.onAddLiquidity.bind(this), this.onRemoveLiquidity.bind(this));
        } else if(!bot.config.gasWatch.includes(to)){
            Lazy.ins().logPending(to, trans.data);
        }
    }
    //测试时间
    async onPendingTs(trans:ethers.providers.TransactionResponse, websocketId:number, beginTime:number){
            //if(trans.to == "******************************************") console.log( " onPending: ", trans);
            if(!bot.oracleV2?.isReady){
                console.log(`${websocketId}) oracleV2 is not ready : oracle(${bot.oracleV2?true:false}), ready(${bot.oracleV2.isReady})`);
                return;
            }
            //Lazy.ins().logTs("[pending time 1]")
            this.gasFilter(trans, websocketId);
            const to = trans.to || macro.zeroAddress;
            if(bot.config.blackList.includes(to) || bot.blackList.has(to)) return; //忽略垃圾机器人
    
            const data = trans.data;
            let selector = macro.uniswapV2FuncMap[data.slice(2,10)];
            if(!selector){
                return;
            } else if(bot.oracleV2.isActiveRouter(to)){
                Lazy.ins().logTs(`[pending time] 1 (${Number(new Date()) - beginTime}ms)`);
                await bot.routers.get(to).decodeData(trans, selector, to, websocketId, this.onSwaps.bind(this), this.onAddLiquidity.bind(this), this.onRemoveLiquidity.bind(this));
                Lazy.ins().logTs(`[pending time] end1 (${Number(new Date()) - beginTime}ms)`);
            } else if(!bot.config.gasWatch.includes(to)){
                Lazy.ins().logPending(to, trans.data);
            }
    }

    onBlock(blockNum:number){
        if(this.blockNum < blockNum) {
            this.highestMixGas = new MixGas();
            this.blockNum = blockNum;
            this.blockBeginTime = new Date().getTime();
            if(blockNum % (~~(120 / bot.config.blockTime)) == 0 && blockNum > 0) this.reportFirstTx = true;
        }
    }

    onSync(address:string, r0:BigNumber, r1:BigNumber, hash:string){
        //if(!bot.oracleV2.isReady) return;
        //if(this.isPairDataReady) {
        bot.oracleV2.data.pm.get(address).onSync(r0, r1, bot.oracleV2.isReady);
    }

    async onSwaps(
        router : string,
        trans : ethers.providers.TransactionResponse,
        dataDecodes : SwapResult[],
        websocketId:number)
    {
        let amounts : number[][] = [];
        let slippages : number[] = [];
        let msg = "";
        let msgExtra = "";
        const bigDealIndexs = [];

        for(const dataDecode of dataDecodes){
            const {amountIn, amountOut, path, exactIn, tokenIn} = dataDecode;
            const bFrom = tokenIn ? bot.token(tokenIn) : bot.token(path[0]);;
            const bTo = bot.token(path[path.length-1]);

            /** 计算amountOut begin */
            const amountInNum = bFrom.toNum(amountIn);
            const amountOutNum = bTo.toNum(amountOut);
            let amount : number[] = [];
            try {
                if(amountIn.eq(macro.bn.zero)){
                    Lazy.ins().log1(`[error] amountIn is zero, from:${trans.from}, hash:${trans.hash}`);
                    return;
                }
                /*if(tokenIn){
                    amount = await bot.oracleV2.getAmountsOutByPairLocal(tokenIn, amountInNum, path); //okx聚合功能未完成
                    amounts.push(amount);
                    slippages.push(0);
                } else*/
                if(exactIn){
                    amount = await bot.oracleV2.getAmountsOutByPath(router, amountIn, path);
                    amounts.push(amount);
                    slippages.push(tools.getOutSlippage(amount[amount.length-1], amountOutNum));
                } else {
                    //Lazy.ins().log(`amountIn: ${amountInNum}, amountOut:${amountOutNum}`);
                    amount = await bot.oracleV2.getAmountsInByPath(router, amountOut, path);
                    amounts.push(amount);
                    slippages.push(tools.getInSlippage(amount[0], amountInNum)); //amountInNum: amountInMax
                }
            } catch(e){
                console.log(e);
                Lazy.ins().log1(`[error] from:${trans.from}`);
                return;
            }
            /** 计算amountOut end */


            /** 添加打印信息 begin */
            msg += `${macro.COLOR.BBlack}${trans.from}${macro.COLOR.Off} (${bFrom.symbol}) ${amountInNum.toFixed(1)} -> (${bTo.symbol}) ${amountOutNum.toFixed(1)} ${dataDecodes.length > 1 ? ", " : ""}`;
            //+ `[${(slippage*100).toFixed(1)}%] `; //滑点
            for(let i = 0 ; i < path.length-1; i++){
                const [token0, token1] = [path[i], path[i+1]];
                const lp = bot.oracleV2.getPair(router, token0, token1);
                if(lp) {
                    const [r0, r1, feeIn] = lp.getReserves(token0);
                    const priceImpact = amount[i] / r0;
                    if(path.length == 2){
                        if(i==0) msg+= "[";
                        msg += `${tools.priceImpactColor(priceImpact)}`;
                        msg += i < path.length-2 ? ", " : "] ";
                    } else {
                        const bToken0 = bot.token(token0);
                        if(i==0) msgExtra += "         ";
                        msgExtra += `(${bToken0.symbol}) ${amount[i].toFixed(2)} `;
                        msgExtra += `[${tools.priceImpactColor(priceImpact)}] -> `;
                        if(i == path.length-2){
                            const bToken1 = bot.token(token1);
                            msgExtra += `(${bToken1.symbol}) ${amount[i+1].toFixed(2)}`;
                        }
                    }
                    //如果是大单交易
                    if(priceImpact > 0.003){
                        bigDealIndexs.push(i);
                        //this.onPump(trans, dataDecode, isEth, websocketId, amountsOut, i);
                        //this.onBullAndBear(trans, dataDecode, isEth, amountsOut, i, slippage, lp);
                    }
                } else {
                    Lazy.ins().log1(`${macro.COLOR.BBlack}   dynamic: ${bot.token(token0).symbol} -> ${bot.token(token1).symbol}${macro.COLOR.Off}`);
                    //自动更新pair
                    
                }
            }
            if(dataDecodes.length > 1 && msgExtra.length > 0) msgExtra += ", ";
            /** 添加打印信息 end */
        }

        /** 打印信息 begin */
        msg += `${macro.COLOR.BBlack}(${bot.config.routers[router].name}) ${this.wss[websocketId].speed.toFixed()}ms${macro.COLOR.Off}`;
        Lazy.ins().logWsTs(websocketId, msg);
        if(msgExtra.length > 0) Lazy.ins().log1(msgExtra);
         /** 打印结束 begin */

        if(bot.config.pump.active){
            const firstPath = dataDecodes[0].path;
            if(bot.blackList.has(trans.from) && firstPath[0] != firstPath[firstPath.length-1]){
                const {COLOR : color} = macro;
                Lazy.ins().log1(`${color.Red}   [pump] blacklist: tx:${trans.hash}, nonce:${trans.nonce} gas:${utils.formatUnits(trans.gasPrice||bot.handle.minGas(), 'gwei')} ${color.Off}`);
            } else {
                //假设swap成功，缓存被交易的pair, 更新为swap后的值
                const pairCache : {[addr:string]:PairExtend} = {};
                let isEoa = false;
                for(let i = 0 ; i < dataDecodes.length; i++){
                    const dataDecode = dataDecodes[i];
                    for(let j = 0 ; j < dataDecode.path.length-1; j++){
                        const _p = bot.oracleV2.getPair(router, dataDecode.path[j], dataDecode.path[j+1]);
                        if(!_p) continue; //data cache没有缓存
                        if(_p.version == macro.PAIR_VERSION.V2_EOA) isEoa = true;
                        const p = _p.clone();
                        p.updateBalance(dataDecode.path[j], amounts[i][j], amounts[i][j+1]);
                        pairCache[p.address] = p;
                    }
                }
                for(let i = 0 ; i < dataDecodes.length; i++){
                    await bot.client.pump.mev(trans, dataDecodes[i].path, amounts[i], router, pairCache, isEoa);
                }
            }
            //Lazy.ins().logTs(`[pending time] end 2`);
        }
        if(bot.config.bot.active || bot.config.bot2.active){
            bigDealIndexs.forEach((index)=>{
                //忽略黑名单
                if(!bot.config.blackList.includes(trans.from)) this.onBullAndBear(trans, dataDecodes[0], dataDecodes[0].isEth, amounts[0], router, index, slippages[0]);
                //if(bot.config.pump.active) bot.client.pump.filter(trans, dataDecode, isEth, websocketId, amountsOut, index, router, speed);
            });
        }
    }

    //TODO: 计算min是否会失败
    onAddLiquidity(trans : ethers.providers.TransactionResponse, tokenA:string, tokenB:string, amountADesired:BigNumber, amountBDesired:BigNumber){
        //type:0 add， 1:remove
        let pair = bot.oracleV2.getPair(trans.to || macro.zeroAddress, tokenA, tokenB);
        if(!pair || pair.address == macro.zeroAddress){
            Lazy.ins().log(`addLiquidity: ${trans.from} router:(${trans.to}) token: ${tokenA} - ${tokenB} not log pairs`);
            //等完成后再检查，确保能拿到fee
            trans.wait().then(x=>{ bot.oracleV2.autoUpdatePair(trans.to || macro.zeroAddress, tokenA, tokenB)}).catch(e=>{});
            return;
        }
        let amountA = bot.token(tokenA).toNum(amountADesired);
        let amountB = bot.token(tokenB).toNum(amountBDesired);
        const {token0, reserve0, reserve1} = pair;
        const [reserveA, reserveB] = token0 == tokenA ? [reserve0, reserve1] : [reserve1, reserve0];
        if(reserveA != 0 && reserveB != 0){
            const amountBOptimal = tools.quote(amountA, reserveA, reserveB);
            if(amountBOptimal <= amountB) {
                amountB = amountBOptimal;
            } else {
                const amountAOptimal = tools.quote(amountB, reserveB, reserveA);
                amountA = amountAOptimal;
            }
        }
        if(bot.config.pump.active) {
            Lazy.ins().log(`addLiquidity: ${macro.COLOR.BBlack}${trans.from}${macro.COLOR.Off} ${bot.token(tokenA).symbol} - ${bot.token(tokenB).symbol}`);
            bot.client.pump.mevLiquidity(pair, tokenA, tokenB, amountA+reserveA, amountB+reserveB, trans);
        }
    }
    async onRemoveLiquidity(trans : ethers.providers.TransactionResponse, tokenA:string, tokenB:string, liquidity:BigNumber){
        //删除
        let pair = bot.oracleV2.getPair(trans.to || macro.zeroAddress, tokenA, tokenB);
        if(!pair || pair.address == macro.zeroAddress){
            Lazy.ins().log(`removeLiquidity: ${trans.from} ${tokenA} - ${tokenB} not log pairs`);
            bot.oracleV2.autoUpdatePair(trans.to || macro.zeroAddress, tokenA, tokenB); //remove的前提是已经存在过了，不需要等trans完成
            return;
        }

        //动态获取pair的totalSupply，计算liquidity和totalSupply的比率
        const totalSupply = await pair.totalSupply();
        const totalSupplyNum = Number(utils.formatEther(totalSupply));
        const liquidityNum = Number(utils.formatEther(liquidity));
        const [ra,rb] = pair.token0 == tokenA ? [pair.reserve0, pair.reserve1] : [pair.reserve1, pair.reserve0];
        const reserveA = ra * liquidityNum / totalSupplyNum;
        const reserveB = rb * liquidityNum / totalSupplyNum;
        if(bot.config.pump.active) {
            Lazy.ins().log(`removeLiquidity: ${macro.COLOR.BBlack}${trans.from}${macro.COLOR.Off} ${bot.token(tokenA).symbol} - ${bot.token(tokenB).symbol}`);
            bot.client.pump.mevLiquidity(pair, tokenA, tokenB, reserveA, reserveB, trans);
        }
    }

    masterAccounts = ["******************************************","******************************************","******************************************"].map( x => x.toLowerCase());
    //夹子
    onBullAndBear(
        trans : ethers.providers.TransactionResponse,
        dataDecode : SwapResult,
        isEth : boolean,
        amountsOut:number[],
        router : string,
        impactPathIndex:number,
        slippage:number)
    {
        const { path, to } = dataDecode;
        const { stableTokens } = bot.config;
        const { bull, bear} = bot.client;
        const amountIn = amountsOut[impactPathIndex];
        const amountOut = amountsOut[impactPathIndex+1];
        const inToken = path[impactPathIndex];
        const outToken = path[impactPathIndex+1];
        const bInToken = bot.token(inToken);
        const bOutToken = bot.token(outToken);
        const lp = bot.oracleV2.getPair(router, inToken, outToken);

        if(this.masterAccounts.includes(to) || !lp) return;
        if(lp.status !== macro.PAIR_STATUS.ACTIVE || lp.f0 > 0 || lp.f1 > 0) return;
        if(slippage < 0) {
            Lazy.ins().log1(`sli: ${(slippage*100).toFixed(2)}%,  slippage < 0, ignore.....`)
            return;
        }
        //大宗买入
        if(stableTokens.includes(inToken) && bOutToken.active && bOutToken.mainStableToken == inToken && !stableTokens.includes(outToken)){
            //const totalImpact = amountIn / bOutToken.getAllReserve().stable;
            const totalImpact = amountOut / bOutToken.sum.token;
            if(totalImpact > 0.003){
                const multi = tools.getDealMulti(totalImpact);
                Lazy.ins().logTs1(`--- BUY ${multi.title} (${bOutToken.symbol}) sli:${(slippage*100).toFixed(2)}% impact:${tools.priceImpactColor(totalImpact)} multi:x${multi.multi} path:${bOutToken.paths.length} n:${trans.nonce} g:${tools.formatGas(trans.gasPrice)}`);
                Lazy.ins().logTs1(`${macro.COLOR.BBlack}    tx: ${trans.hash}${macro.COLOR.Off}`);

                const isAttacker = bull.history.isAttacker(bOutToken.address, trans.from, true);
                if(isAttacker) return;
                
                this.gasTrackEnable = true;
                
                bull.onPut(trans, dataDecode, multi.multi, slippage, outToken, isEth, totalImpact, amountIn, amountOut);
                bear.onPut(trans, dataDecode, multi.multi, slippage, outToken, isEth, totalImpact, amountIn, amountOut);
                trans.wait().then((receipt)=>{
                    bot.receiptBlockNum(receipt, `@@ (${bOutToken.symbol}) whale buy `);
                    this.resetGas(trans.from);
                }).catch((receipt)=> {
                    Lazy.ins().logTs1(`@@ (${bOutToken.symbol}) oops...${macro.COLOR.BPurple}whale${macro.COLOR.Off} buy fail...`);
                    this.resetGas(trans.from);
                });

            } else {
                Lazy.ins().logTs1(`    not enough total impact (${bOutToken.symbol}): ${tools.priceImpactColor(totalImpact)}, path:${bOutToken.paths.length}`);
            }

            bull.history.add(bOutToken.address, trans.from, true, totalImpact > 0.003);

        } else if(stableTokens.includes(outToken) && bInToken.active && bInToken.mainStableToken == outToken && !stableTokens.includes(inToken)){
        //大宗卖出
            const totalImpact = amountIn / bInToken.sum.token;
            if(totalImpact > 0.003){
                const multi = tools.getDealMulti(totalImpact);
                Lazy.ins().logTs1(`--- SELL ${multi.title} (${bInToken.symbol}) sli:${(slippage*100).toFixed(2)}% impact:${tools.priceImpactColor(totalImpact)} path:${bInToken.paths.length} multi:x${multi.multi} n:${trans.nonce} g:${tools.formatGas(trans.gasPrice)}`);
                Lazy.ins().logTs1(`${macro.COLOR.BBlack}    *** tx: ${trans.hash}${macro.COLOR.Off}`);

                const isAttacker = bull.history.isAttacker(bInToken.address, trans.from, false);
                if(isAttacker) return;

                this.gasTrackEnable = true;
                bull.onPull(trans, dataDecode, multi.multi, slippage, inToken, isEth, totalImpact, amountIn, amountOut);
                bear.onPull(trans, dataDecode, multi.multi, slippage, inToken, isEth, totalImpact, amountIn, amountOut);
                trans.wait().then((receipt)=>{
                    bot.receiptBlockNum(receipt, `## (${bInToken.symbol}) whale sell `);
                    this.resetGas(trans.from);
                }).catch((receipt)=> {
                    Lazy.ins().log1(`## (${bInToken.symbol}) oops...${macro.COLOR.BPurple}whale${macro.COLOR.Off} sell fail...`);
                    this.resetGas(trans.from);
                });
            } else {
                Lazy.ins().logTs1(`    not enough total impact (${bOutToken.symbol}): ${tools.priceImpactColor(totalImpact)},  path: ${bInToken.paths.length}`);
            }
            bull.history.add(bInToken.address, trans.from, false, totalImpact > 0.003);
        }
    }

    //TODO: 功能未实现
    resetGas(fromAddr : string){
        bot.client.bull.txBidCache.clear();
        bot.client.bear.txBidCache.clear();
        this.highestMixGas.reset();
        this.gasTrackEnable = false;
    }

    gasFilter(trans:ethers.providers.TransactionResponse, websocketId=0){
        if(!trans.to) return;

        if(trans.to == bot.config.bot.address || trans.to == bot.config.bot2.address){
            Lazy.ins().logWsTs1(websocketId, `${macro.COLOR.Green}>>>>>${macro.COLOR.Off} ${trans.from.slice(0,5)} -> ${trans.to} `
                + `n:${trans.nonce} g:${macro.COLOR.Green}${ trans.maxFeePerGas ? tools.formatGas(trans.maxPriorityFeePerGas) + " / " + tools.formatGas(trans.maxFeePerGas) : tools.formatGas(trans.gasPrice)}${macro.COLOR.Off}`);
            this.nonce.update(trans.from, false, bot.config.blockTime * 5, false); //不复写
            return;
        }

        let mixGas = new MixGas(trans);
        if(mixGas.value().gt(this.highestMixGas.value())){
            this.highestMixGas = mixGas;
            if(this.gasTrackEnable){
                bot.client.bull.onHigherGas(trans, undefined);
                bot.client.bear.onHigherGas(trans, undefined);
            }
        }

        bot.client.trash.onHigherGas(trans);

        if(bot.config.gasWatch.includes(trans.to)){
            Lazy.ins().logWsTs1(websocketId, `${macro.COLOR.Blue}>>>>>${macro.COLOR.Off} ${trans.from.slice(0,5)} -> ${trans.to} `
                + `n:${trans.nonce} g:${macro.COLOR.Blue}${ trans.maxFeePerGas ? tools.formatGas(trans.maxPriorityFeePerGas) + " / " + tools.formatGas(trans.maxFeePerGas) : tools.formatGas(trans.gasPrice)}${macro.COLOR.Off}`);
        }

    }

    //估算距离下个块的剩余时间
    nextBlockTime(){
        if(bot.config.blockTime < 1) return 0;
        const now = Number(new Date); //毫秒
        const remain = this.blockBeginTime + (bot.config.blockTime * 1000) - now; //3秒后
        return remain > 0 ? remain : 0;
    }

    sendTransactionAll(signTx:string, onPending?:(tx:string)=>void,  onSuccess?:(receipt:ethers.providers.TransactionReceipt)=>void, pendingFail?:(e:any)=>void, miningFail?:(e:any)=>void){
        if(this.wss.length == 1) return this.sendTransaction(signTx, onPending, onSuccess, pendingFail, miningFail);
        const promises = this.wss.map(ws=>{ return ws.provider.send("eth_sendRawTransaction", [signTx])});
        Promise.any(promises).then(tx=>{
            this.wss[0].provider.getTransactionReceipt(tx).then(receipt => {
                if(receipt) return receipt;

                //获取到receipt之后的n个block检查是否被打包，
                let tryBlock = 0;
                const handle = async ()=> {
                    tryBlock++;
                    //Lazy.ins().log(`checking tx(${tryBlock}): ${tx}, ${bot.event.listenerCount(macro.EVENT.WS_ON_BLOCK)}`);
                    if(tryBlock > 5){
                        //timeout or replaced
                        pendingFail && pendingFail(tx);
                        return;
                    } else {
                        let res = await this.wss[0].provider.getTransactionReceipt(tx);
                        if(res){
                            if(res.status == 0) {
                                miningFail && miningFail(tx);
                            } else {
                                onSuccess && onSuccess(res);
                            }
                        } else {
                            bot.event.once(macro.EVENT.WS_ON_BLOCK, handle.bind(this));
                        }
                    }
                };
                bot.event.once(macro.EVENT.WS_ON_BLOCK, handle.bind(this));

            }).catch( e =>{
                console.log("[sendTransactionAll] error \n", e);
                //可能是网络异常
                //miningFail && miningFail(e);
            });
            onPending && onPending(tx);
        }).catch(e=>{
            //pending错误, 未打包
            pendingFail && pendingFail(e);
        });
        Lazy.ins().logTs(`sendTransactionAll`);
    }

    sendTransaction(signTx:string, onPending?:(tx:string)=>void, success?:(receipt:ethers.providers.TransactionReceipt)=>void, pendingFail?:(e:any)=>void, miningFail?:(e:any)=>void){
        this.wss[0].provider.send("eth_sendRawTransaction", [signTx]).then(tx=>{
            this.wss[0].provider.getTransactionReceipt(tx).then(receipt => {
                if(receipt) return receipt;

                //获取到receipt之后的n个block检查是否被打包，
                let tryBlock = 0;
                const handle = async ()=> {
                    tryBlock++;
                    //Lazy.ins().log(`checking tx(${tryBlock}): ${tx}, ${bot.event.listenerCount(macro.EVENT.WS_ON_BLOCK)}`);
                    if(tryBlock > 5){
                        //timeout or replaced
                        pendingFail && pendingFail(tx);
                        return;
                    } else {
                        let res = await this.wss[0].provider.getTransactionReceipt(tx);
                        if(res){
                            if(res.status == 0) {
                                miningFail && miningFail(tx);
                            } else {
                                success && success(res);
                            }
                        } else {
                            bot.event.once(macro.EVENT.WS_ON_BLOCK, handle.bind(this));
                        }
                    }
                };
                bot.event.once(macro.EVENT.WS_ON_BLOCK, handle.bind(this));

            }).catch( e =>{
                console.log("[sendTransaction] error \n", e);
                //可能是网络异常
                //miningFail && miningFail(e);
            });
            onPending && onPending(tx);
        }).catch(e=>{
            if(e.body){
                Lazy.ins().log1(`${macro.COLOR.BBlack}${Object.values(e.body).join("")}${macro.COLOR.Off}`);
            } else if(e.response){
                Lazy.ins().log1(`${e.response}${macro.COLOR.Off}`);
            } else {
                Lazy.ins().log1(`${macro.COLOR.Red}timeout or replaced${macro.COLOR.Off}`);
            }
            //pending错误, 未打包
            pendingFail && pendingFail(e);
        });
        Lazy.ins().logTs("sendTransaction");
    }

}