import bot from '../bot';
import fs from "fs";
import { ethers, BigNumber, utils, UnsignedTransaction } from 'ethers';
import {TransactionResponse } from "@ethersproject/abstract-provider";
import TokenBalance, { TokenBalanceItem } from "./comp/tokenBalance";
import ExTimer from './comp/ExTimer';
import tools from './tools';

import TransPool from './comp/TransPool';
import { macro } from './macro';
import { ExContract, ExWallet } from './comp/EthersWrapper';
import HashPool from './comp/HashPool';
import DataType, { SwapAmoutsOut, BaseSwapExData, SwapResult, ServerWhale, ServerFeedList, SumToken } from "./type/DataType";
import { TokenExtend } from './type/Token';
import WasmSign from './comp/WasmSign';
import Lazy from './lazy/LazyController';
import SwapHistory from './comp/SwapHistory';

export default class BotBase {
    private botAddr = "";
    operators : Array<ExWallet> = [];

    private bots : Array<Array<ExContract>> = [];
    private operatorAddrs : Array<string> = [];

    //dealersMap : Map<string, ExContract> = new Map();

    greedMode = false; //贪婪模式，只要有盈利就抛出，通常用于超大笔买卖后降低bot的利润的期望值

    trans = new TransPool();
    tokenBalance = new TokenBalance(); //bull 维护当前持仓以及购入价格, bear 维护卖出价格和卖出token数量
    tokenBuyBack = new TokenBalance();

    static blackListList : Array<string> = [];

    txBidCache  = new Map<string, BaseSwapExData>(); //记录需要抢先交易的tx，如果有更高的gas交易发生就发送更高的gas交易

    history = new SwapHistory();

    constructor(wallets:{[k:string]:string}, addr:string){
        this.botAddr = addr;
        for(const key of Object.values(wallets)){
            const wallet = bot.newWallet(key);
            this.operators.push(wallet);
        }

        this.bots = bot.initClient(wallets, addr);
        this.operatorAddrs = Object.keys(wallets);
        if(bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.UPDATE) return;

        setTimeout(()=>{ this.updateBalance() }, 1000 * 6);
        //循环逻辑改到函数内递归实现
        ExTimer.setIntervalOld(()=>{ this.updateBalance() }, 1000 * 10 * bot.config.blockTime);

        setTimeout(()=>{ this.autoBuyBack() }, 1000 * 7);
        ExTimer.setIntervalOld(()=>{ this.autoBuyBack() }, 1000 * 7 * bot.config.blockTime); //每7个块检查一次是否需要回购
        
    }

    loadCache(path : string){
        const result = new TokenBalance();
        const file = bot.getLogPath(path);
        //console.log(macro.getLogPath(file, false));
        if(fs.existsSync(file)){
            try {
                let cache = JSON.parse(fs.readFileSync(file, "utf-8")) as {data:Array<{name:string, address:string, usdt:number,token:number}>};
                //console.log(cache);
                cache.data.forEach((b)=>{
                    try {
                        let token : TokenExtend
                        token = bot.token(b.address.toLowerCase());
                        if(!token) return;
                        result.setData(token.address.toLowerCase(), new TokenBalanceItem({
                            usdt : {
                                num : b.usdt || 0,
                                bn : b.usdt > 0 ? bot.token(token.mainStableToken).toBigNumber(b.usdt) : macro.bn.zero
                            },
                            token : {
                                num : b.token || 0,
                                bn : utils.parseUnits((Number(b.token) || 0).toString(), token.decimals)
                            }
                        }));
                    } catch(e){
                        console.log(e);
                        return;
                    }
                });
            } catch(e) { console.log(e)};
        }
        return result;
    }

    saveCache(path : string, balance:TokenBalance){
        const file = bot.getLogPath(path);
        let localData : {data:Array<{name:string, address:string, usdt:number,token:number}>} = {data:[]};
        for(const [k,v] of Object.entries(balance.data())){
            if(v.token.num > 0 || v.usdt.num > 0){
                localData.data.push({
                    name : bot.token(k).symbol,
                    address: k, 
                    usdt : v.usdt.num,
                    token : v.token.num
                })
            }
        }
        //console.log(localData);
        tools.writeFileSync(file, JSON.stringify(localData, null, "\t"));
    }

    getOperator(addrOfIndex=0, websocketId = 0) {
        return this.bots[websocketId][addrOfIndex];
    }

    getOperatorAddress(index = 0, delta = 0){
        return this.operatorAddrs[(index + delta) % 10].toLocaleLowerCase();
    }

    async getBalance(token:string){
        /*const promises = [];
        for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
            if(!bot.handle.wss[i].onlyBroadcast) promises.push(this.getOperator(undefined, i).ins().functions.getBalance(token));
        }
        const res : Array<BigNumber> = await Promise.any(promises);
        */
       try {
            const res : BigNumber[] = await this.getOperator().ins().functions.getBalance(token);
            return {
                bn : res[0],
                num : bot.token(token).toNum(res[0])
            }
       } catch(e){
            console.log(`${macro.COLOR.Red} error balance of: ${token} ${macro.COLOR.Off}`)
            return {
                bn : macro.bn.zero,
                num : 0
            }
       }

    }

    hashPool = new HashPool(100);
    async swap(input : BaseSwapExData, skipLoop = false){
        const {data, whale, mixGas, frontRun} = input;
        //初始化操作员
        if(input.operatorIndex == -1) input.operatorIndex = bot.getOperatorIndex(whale.addr, bot.handle.blockNum);
        //console.time(`   (${input.operatorIndex}) swap sign time`);
        const opAddr = this.getOperatorAddress(input.operatorIndex);
        const op = this.operators[input.operatorIndex].ins();
        if(!input.nonce) input.nonce = await bot.handle.nonce.useSync(opAddr);

        const toToken = data.paths[0][data.paths[0].length-1];
        if(input.bid && !skipLoop){
            //需要竞争gas
            console.log(`${macro.COLOR.BBlack}    -- bidding mode  --${macro.COLOR.Off}`);
            this.txBidCache.set(whale.addr, input);
        }
        if(mixGas.moreBigGas && !skipLoop && input.bid){
            this.onHigherGas(undefined, mixGas.moreBigGas); //one more bidding for one
        }

        //生成服务端请求数据
        //const serverSwapData = DataType.swapData2Serer(input.data);
        //const serverWhale = DataType.whale2Server(input.whale);
        //if(frontRun && !skipLoop)this.verifyWhale(serverWhale); //第一次竞价检查是否假交易

        const funcData = bot.client.iBot.encodeFunctionData("swapExWithWhaleSmart", [
            input.data.tokenIn,
            input.data.in.bn,
            input.data.outSlippage.bn,
            input.data.amountsIn,
            input.data.pairs.map( p => {return "0x" + DataType.pairToServerDataBytes(p, p.map(_=>0))}),
            input.whale
        ]);

        const tx : UnsignedTransaction = {
            //from : opAddr,
            to : this.botAddr,
            gasLimit : input.data.amountsIn.length * macro.abi.botGasSingleRouter,
            data : funcData,
            nonce : input.nonce,
            type : bot.handle.block.type,
            chainId : bot.handle.block.chainId,
        };

        if(!input.frontRun && bot.chain == macro.CHAIN.MILKADA){
            mixGas.sub(); //milkada跟在后面需要减1wei
        }
        mixGas.attach(tx);

        //const signData = await op.signTransaction(tx);
        //const signData = WasmSign.sign(tx, op.privateKey);
        //const signData = WasmSign.sign(tx, op.privateKey);
        const signData = WasmSign.sign_full(tx, op.privateKey);
        //const signData = await bot.signer.sign(tx, op.privateKey);

        Lazy.ins().logTs1(`*** op: (${input.operatorIndex}) ${opAddr}, g:${utils.formatUnits(mixGas.value(), 'gwei')}, n:${input.nonce} b:${input.bidTime}`);
        //console.timeEnd(`   (${input.operatorIndex}) swap sign time`);
        if(bot.mode == macro.BOT_MODE.DEBUG || bot.mode == macro.BOT_MODE.TEST) return
        
        for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
            if(bot.handle.wss[i].enablePost)  this._swap(input, signData, i, toToken);
        }
        
        
        /*
        bot.handle.sendTransactionAll(signData, (tx)=>{
            Lazy.ins().logTs1("pending...");
            this.trans.add(tx, toToken);
        }, (receipt)=>{
            input.onFinish && input.onFinish(receipt);
        },
        (pendingFail)=>{
            if(pendingFail.body){
                Lazy.ins().log1(`${Object.values(pendingFail.body).join("")}`);
            } else if(pendingFail.response){
                Lazy.ins().log1(`${pendingFail.response}${macro.LINUX_COLOR.Off}`);
            }
        },
        (miningFail) => {
            if(miningFail.body){
                Lazy.ins().log1(`${Object.values(miningFail.body).join("")}`,);
            } else if(miningFail.response){
                Lazy.ins().log1(`${miningFail.response}${macro.LINUX_COLOR.Off}`);
            }
        });
        */
    }

    async _swap(input:BaseSwapExData, signData : string, websocketId = 0, toToken:string){
        const req : Promise<TransactionResponse> = bot.handle.wss[websocketId].provider.sendTransaction(signData);
        let tx : TransactionResponse;
        try {
            tx = await req;
        } catch(e : any) {
            if(e.body){
                Lazy.ins().log1(`${Object.values(e.body).join("")}`);
            } else if(e.response){
                Lazy.ins().log1(`${e.response}${macro.COLOR.Off}`);
            }
            return false;
        }
        this.trans.add(tx.hash, toToken);
        try {
            const receipt : ethers.providers.TransactionReceipt = await tx.wait();
            //相同 hash只触发一次
            if(this.hashPool.enable(receipt.transactionHash)) input.onFinish && input.onFinish(receipt);
        } catch(e : any) {
            if(e.body){
                Lazy.ins().log1(`${Object.values(e.body).join("")}`,);
            } else if(e.response){
                Lazy.ins().log1(`${e.response}${macro.COLOR.Off}`);
            }
            //console.log(e);
            if(this.hashPool.enable(tx.hash)) input.onFail && input.onFail(e);
        }
        this.trans.remove(tx.hash);
    }

   /** 获取最佳报价和路由 */
    async getBestAmountOut(token:string, amountIn:BigNumber, isBuy=true, outMulti=1, fromServer=false){
        //outMulti = 0.3; //TODO: test
        const bToken = bot.token(token);
        const bFrom = bot.token(isBuy ? bToken.mainStableToken : token);
        const bTo = bot.token(isBuy ? token : bToken.mainStableToken);

        const s = bot.oracleV2.getSwapRouters(token, amountIn, bToken.maxLp || bot.config.maxLp);
        //const s = bot.oracleV2.getSwapRoutersV2(token, amountIn, 10);
        //需要反转路由(卖出) 配置表上的是stable-token
        if(!isBuy){
            s.paths = s.paths.map((x)=> x.reverse());
            s.pairs = s.pairs.map((x)=> x.reverse());
        }
        const result = new SwapAmoutsOut();
        result.tokenIn = isBuy ? bToken.mainStableToken : bToken.address;
        //本地计算, 不检查输出和最佳路由
        if(!fromServer){
            result.routers = s.routers;
            result.amountsIn = s.amountsIn;
            result.paths = s.paths;
            result.pairs = s.pairs;
            result.in.bn = amountIn;
            result.in.num = bFrom.toNum(amountIn);
            //计算本地最佳输出
            let multiOut = 0; //多路径输出结果

            const singleOut = await bot.oracleV2.getAmountsOutByPair(bFrom.address, amountIn, s.pairs[0]); //单路径输出结果
            if(s.routers.length > 1) {
                for(let i = 0 ; i < s.routers.length; i++){
                    const outs = await bot.oracleV2.getAmountsOutByPair(bFrom.address, s.amountsIn[i], s.pairs[i]);
                    multiOut += outs;
                }
            }
            //console.log(singleOut, multiOut);
            if(s.routers.length == 1 || multiOut < singleOut){
                result.out.num = singleOut;
                result.outSlippage.num = singleOut * outMulti;
            } else {
                result.out.num = multiOut;
                result.outSlippage.num = singleOut * outMulti;
            }

            result.out.bn = bTo.toBigNumber(result.out.num);
            result.outSlippage.bn = bTo.toBigNumber(result.outSlippage.num);
            return result;
        }

        //for test
        /*
        s.pairs.map( p =>{return DataType.pairToServerData(p)}).forEach(pairs=>{
            console.log("-----------------");
            pairs.forEach(p=>{
                console.log(`ServerPairData: ${p.addr} ver:${p.version} fee0:${p.fee0.toNumber()} fee1:${p.fee1.toNumber()}`);
            });
        });
        console.log(JSON.stringify(bTo));
        */

        //单websocket请求
        const res : {sumOut:BigNumber}  = await this.getOperator().ins().functions.getAmountsExSmartOnline(result.tokenIn, amountIn, s.amountsIn, s.pairs.map( p =>{
            const fees = p.map(_p => 0); //TODO: bull和bear暂时不参与有fee的交易对
            return DataType.pairToServerData(p, fees);
        }));
        //console.log(res);

        /*
        const promises = [];
        for(let i = 0 ; i < bot.handle.wss.length; i++){
            if(!bot.handle.wss[i].onlyBroadcast) {
                promises.push(this.getOperator(undefined, i).ins().functions.getAmountsExSmartOnline(result.tokenIn, amountIn, s.amountsIn, s.pairs));
            }
        }
        const res : {sumOut:BigNumber}  = await Promise.any(promises);
        */

        result.out.bn = res.sumOut;
        result.out.num = bTo.toNum(res.sumOut);
        result.outSlippage.bn = outMulti !== macro.slippage.zero ? result.out.bn.mul(BigNumber.from((outMulti * 1000).toFixed())).div(macro.bn.k) : result.out.bn;
        result.outSlippage.num = bTo.toNum(result.outSlippage.bn);

        result.amountsIn = s.amountsIn;
        result.routers = s.routers
        result.paths = s.paths;
        result.pairs = s.pairs;
        result.in.bn  = amountIn;
        result.in.num = bFrom.toNum(amountIn);
        return result;

    }

    onHigherGas(trans? : ethers.providers.TransactionResponse, forceGasPrice?:BigNumber, isSecondBid=false){
        const gas = trans ? (trans.maxPriorityFeePerGas || trans.gasPrice || forceGasPrice) : forceGasPrice;
        if(!gas) return;
        if(gas.gt(utils.parseUnits(bot.config.gasMax.toFixed(9), 'gwei'))) return; //reachMaxGas
        if(this.txBidCache.size == 0) return;

        for(let [k,v] of this.txBidCache.entries()){
            if(v.mixGas.value().gte(gas)) continue; //参与所有竞价
            const data = {...v};
            
            data.mixGas = data.mixGas.lessMore(trans).clone();

            data.bid = false; //bid过了不再加入缓存
            data.bidTime++;
            this.txBidCache.set(k, data);
            this.swap(data, true);
            
            if(bot.chain == macro.CHAIN.GLMR && data.bidTime < 10){
                const data2 = {...data};
                data2.bidTime++;
                data2.mixGas = data2.mixGas.lessMore().clone();
                this.txBidCache.set(k, data2);
                setTimeout(()=>{
                    this.swap(data2, true);
                }, 70);
            }
        }
    }

    logTrade(file:string, tx:string, token:string, tokenCount:number, stable:string, stableCount:number, isBuy=false, exStr=""){
        //const str = `${isBuy?'BUY ':'SELL'} | ${tx} | ${bot.token(token).symbol} | ${+tokenCount.toFixed(5)} | ${bot.token(stable).symbol} | ${+stableCount.toFixed(5)}${exStr!="" ? " | " + exStr : ""}`;
        //bot.localLog.append_old(file, str);

        bot.localLog.append(file, tx, [
            isBuy ? 'BUY ':'SELL',
            bot.token(token).symbol,
            `${+tokenCount.toFixed(5)}`,
            bot.token(stable).symbol,
            `${+stableCount.toFixed(5)}${exStr!="" ? " | " + exStr : ""}`,
        ]);
    }

    async checkFeed(){
        let checkList : ServerFeedList[] = [];
        const feedList : ServerFeedList[] = [];
        const { feedMin, feedMax, maxOperator} = bot.config;
        const min = utils.parseEther(feedMin.toString());
        const max = utils.parseEther(feedMax.toString());
        const m4 = BigNumber.from('4');
        const mPump = BigNumber.from((bot.config.feedPumpMulti * 100).toFixed());
        if(bot.config.pump.active) {
            Object.keys(bot.walletData.pump).slice(0, maxOperator).forEach((x)=>{
                let item = new ServerFeedList();
                item.addr = x;
                item.min = min.mul(mPump).div(macro.bn.h);
                item.max = max.mul(mPump).div(macro.bn.h);
                item.balance = macro.bn.zero;
                checkList.push(item);
            });
        }
        if(bot.config.pump.active || bot.config.bot.active || bot.config.trash.active){
            Object.keys(bot.walletData.bull).slice(0, maxOperator).forEach((x)=>{
                let item = new ServerFeedList();
                item.addr = x;
                item.min = min;
                item.max = max;
                item.balance = macro.bn.zero;
                checkList.push(item);
            });
        }
        if(bot.config.bot2.active){
            Object.keys(bot.walletData.bear).slice(0, maxOperator).forEach((x)=>{
                let item = new ServerFeedList();
                item.addr = x;
                item.min = min.div(m4);
                item.max = max.div(m4);
                item.balance = macro.bn.zero;
                checkList.push(item);
            });
        }

        if(bot.config.junk.active){
            const m10 = BigNumber.from('10');
            Object.keys(bot.walletData.bear).slice(0, 1).forEach((x)=>{
                let item = new ServerFeedList();
                item.addr = x;
                item.min = min.mul(m10);
                item.max = max.mul(m10);
                item.balance = macro.bn.zero;
                checkList.push(item);
            });
        }
        if(bot.chain == macro.CHAIN.ECO || bot.chain == macro.CHAIN.ELA){
            checkList = tools.shuffleArray(checkList);
        }
        let rand_index = tools.randomInt(0, bot.config.maxOperator-1);
        let [results, total] : [ServerFeedList[], BigNumber] = await this.getOperator(rand_index).ins().functions.checkFeed(checkList, { gasLimit: 3000000 });
        //console.log(results);
        results.forEach((r)=>{
            console.log(`${r.addr} ${utils.formatEther(r.min).slice(0, 7)}/${utils.formatEther(r.max).slice(0, 7)} balance: ${utils.formatEther(r.balance||macro.bn.zero).slice(0, 7)} feed: ${utils.formatEther(r.feedAmount).slice(0, 7)}`);
            if(r.feedAmount.eq(macro.bn.zero)) return;
            feedList.push(r);
        });
        console.log(`total feed gas: ${utils.formatEther(total)}`);
        return {
            list : feedList,
            total : total
        };
    }

    async feed(feedList : ServerFeedList[], total:BigNumber){
        const id = tools.randomInt(0,bot.config.maxOperator);
        const op = this.getOperator(id).ins();
        const address = await op.signer.getAddress();
        const nonce = await bot.handle.nonce.use(address);
        console.log(`${id} ${address} ${nonce}`);
        let gasMin = bot.handle.minGas();
        const gasMax = utils.parseUnits(bot.config.gasMax.toFixed(9), 'gwei');
        if(gasMin.gt(gasMax)) gasMin = gasMax;

        const overrides = {
            gasLimit: 100000 * feedList.length,
            nonce: nonce,
            gasPrice : gasMin
        };
        let feed = await op.functions.feed(feedList, bot.config.eth, total, overrides);
        bot.handle.nonce.update(address);
        //console.log(feed);
        console.log(`(${id}) feed success : ${op.address}`);
    }

    async batchFeed(){
        if(bot.cmd == "debug") return;
        let {list, total} = await bot.client.bull.checkFeed();
        if(list.length  == 0){
            console.log("no need to fill");
            return;
        }
        await this.feed(list, total);
    }

    async delay(){
        return true;
        /*let remain = bot.handle.nextBlockTime();
        if(remain > 0){
            console.log(" ----- until next block ---- " , remain, ' ms');
            if(remain > 600) remain = 600;
            await tools.sleep(remain);
            return true;
        }
        return false;*/
    }

    async onPut(
        trans:ethers.providers.TransactionResponse,
        dataDecode:SwapResult,
        multi:number,
        slippage:number,
        token:string,
        isEth:boolean,
        totalImpact: number,
        pairIn:number,
        pairOut:number){
    }

    async onPull(
        trans:ethers.providers.TransactionResponse,
        dataDecode:SwapResult,
        multi:number,
        slippage:number,
        token : string,
        isEth : boolean,
        totalImpact : number,
        pairIn:number,
        pairOut:number){
    }

    async updateBalance(selectedTokens:string[] = [], fromServer=true, force=false) : Promise<boolean>{ return false }
    async autoBuyBack() : Promise<boolean>{ return false }

    clearCache(){
        this.trans.clear();
        this.txBidCache.clear();
    }

}