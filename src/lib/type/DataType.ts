import bot from '../../bot';
import { ethers, BigNumber, utils, UnsignedTransaction } from 'ethers';
import { macro } from '../macro';
import Lazy from '../lazy/LazyController';
import { Pair } from '../type/Pair';

export class SwapAmoutsOut {
    routers: string[] = [];
    paths:string[][] = [[]];

    tokenIn = "";
    amountsIn : Array<BigNumber> = [];
    pairs:string[][] = [[]];

    in : {num:number, bn:BigNumber} = {num:0, bn:BigNumber.from('0')};
    out : {num:number, bn:BigNumber} = {num:0, bn:BigNumber.from('0')}; //真实输出的价格
    outSlippage : { num:number, bn:BigNumber} = { num:0, bn:BigNumber.from('0')}; //实际交易的价格
}

export class ServerWhale {
    public addr = macro.zeroAddress;
    public token = macro.zeroAddress;
    public amount = macro.bn.zero;;
    public isEth = false;
    public isGreater = true;

    public spender = macro.zeroAddress;
    public spenderToken = macro.zeroAddress;
    public spenderAmount = macro.bn.zero;
}

export class ServerSwapData {
    public tokenIn = "";
    public sumIn = BigNumber.from('0');
    public amountsIn :BigNumber[] = [];
    public pairs:string[][] = [];
    public minOut = BigNumber.from('0');
}

export class ServerFeedList {
    public addr = macro.zeroAddress;
    public min = macro.bn.zero;
    public max = macro.bn.zero;
    public balance = macro.bn.zero;
    public feedAmount = macro.bn.zero;
}

export class ServerPairData {
    public addr = macro.zeroAddress;
    public version = 0;
    public fee = macro.bn.k10; //只需记录单个方向的fee
    public fp = macro.bn.k10;  //pair fee

    //public version = macro.bn.zero;
}

export class ServerPumpData {
    public convertEth = macro.bn.zero;
    public tokenIn0or1 = macro.bn.zero;
    public calc = macro.bn.zero;
    public gasLimit = macro.bn.zero;
    public cost = macro.bn.zero;
    public amountIn = macro.bn.zero;
    public pairs : ServerPairData[] = [];
}

export class WhaleSwapData {
    public addr : string;
    public token : string;
    public amount : BigNumber;
    public isEth : boolean;
    public isGreater : boolean;

    public spender = macro.zeroAddress;
    public spenderToken = macro.zeroAddress;
    public spenderAmount = macro.bn.zero;

    constructor(
        addr = macro.zeroAddress,
        token = macro.zeroAddress,
        amount=BigNumber.from('0'),
        isEth = false,
        isGreater=true,
        spender = macro.zeroAddress,
        spenderToken = macro.zeroAddress,
        spenderAmount = macro.bn.zero
    ){
        this.addr = addr;
        this.token = token;
        this.amount = amount;
        this.isEth = isEth;
        this.isGreater = isGreater;

        this.spender = spender;
        this.spenderToken = spenderToken;
        this.spenderAmount = spenderAmount;
    }
}

export class GetPairInfoResultDesc {
    addr = "";
    token0 = "";
    token1 = "";
    s0 = "";
    s1 = "";
    r0 = macro.bn.zero;
    r1 = macro.bn.zero;
    d0 = macro.bn.zero;
    d1 = macro.bn.zero;
    lastUpdate = macro.bn.zero;
    id = macro.bn.zero;
    stable = false;
}

export class CheckMevsInputDesc {
    tokenIn0or1 = false;
    pairs : Pair[] = [];
}

export class CheckMevsResultDesc {
    gas = macro.bn.zero;
    fee0 : BigNumber[] = [];
    fee1 : BigNumber[] = [];
}

export class MevPath {
    s0 = ""; //stable0
    s1 = ""; //stable1
    tokenIn0or1 : number[] = []; //0:token0, 1:token1
    pairs : string[] = [];
    fees0 : number[] = []; //正向fee
    fees1 : number[] = []; //逆向fee
    weight = 0; //最低的pair价值
    status0 = macro.MEV_STATUS.UNCHECKED;  //正向
    status1 = macro.MEV_STATUS.UNCHECKED; //逆向
    gas = 0; //最多的gas
    type = macro.MEV_TYPE.DXDY;
    convertEth = 0; //0:same, 1:不同的eth搬砖
}

export class FindPathResult {
    stable = "";
    tokenIn0or1 = 0;
    convertEth = 0;
    fees : number[] = [];
    mevType = macro.MEV_TYPE.DXDY;

    gasCost : BigNumber = macro.bn.zero; //gas消耗等值的stable数量
    pairs : string[] = [];
    amount : BigNumber = macro.bn.zero; //amountIn
    gas = 0; //消耗的gas
    //用于统计
    reward = 0;      //reward的token数
    rewardValue = 0; //reward的价值

    //用于FindPathBid
    maxFeePerGas = macro.bn.zero;
    gasLimit = macro.bn.zero;

    //用于计算2层收益
    amounts : number[] = [];
    tokenOuts : string[] = [];
}

export class FindPathResultsToServerPumpData {
    totalGas = 0;
    totalRewardValue = 0;
    data : ServerPumpData[] = []; 
    encodePackedData  = "";
}

export class FindPathController {
    private indexs : string[] = [];
    public totalRewardValue = 0;
    public totalGas = 0;
    _data : FindPathResult[] = [];

    add(r:FindPathResult[]){
        r.forEach(_r=>{
            const key = _r.pairs.join(",");
            if(!this.indexs.includes(key)){
                this.indexs.push(key);
                this._data.push(_r);
                this.totalRewardValue += _r.rewardValue;
                this.totalGas += _r.gas;
            }
        });
    }

    sort(){
        this._data.sort((a,b)=>{
            return b.rewardValue - a.rewardValue
        });
    }

    data(){
        return this._data;
    }
}


export class MixGas {
    gasPrice?:BigNumber;
    maxFeePerGas?:BigNumber; //最大fee
    maxPriorityFeePerGas?:BigNumber //给矿工的费用，一般 < maxfeepergas
    gasLimit : BigNumber = macro.bn.zero;
    type = 0;

    gasCost : BigNumber = macro.bn.zero;
    gasCostTable : {[stable:string]:{bn:BigNumber, num:number}} = {};
    moreBigGas?:BigNumber;

    constructor(trans?: ethers.providers.TransactionResponse){
        if(trans){
            const {gasPrice, maxFeePerGas, maxPriorityFeePerGas, type} = trans;
            if(type == 2){
                //eip1559
                this.maxFeePerGas = maxFeePerGas;
                this.maxPriorityFeePerGas = maxPriorityFeePerGas;
                this.type = type;
            } else {
                this.gasPrice = gasPrice;
                this.type = 0;
            }
            /*
            if(maxFeePerGas && maxPriorityFeePerGas){
                //eip1559
                this.maxFeePerGas = maxFeePerGas;
                this.maxPriorityFeePerGas = maxPriorityFeePerGas;
            } else {
                //在eip1559上使用旧gas的tx
                if(bot.handle.block.isEIP1559){
                    this.maxFeePerGas = gasPrice;
                    this.maxPriorityFeePerGas = gasPrice;
                } else {
                    this.gasPrice = gasPrice;
                }
            }*/
        } else {
            if(bot.handle){
                if(bot.handle.block.isEIP1559){
                    //eip1559
                    this.maxFeePerGas = bot.handle.minGas();
                    this.maxPriorityFeePerGas = this.maxFeePerGas;
                } else {
                    this.gasPrice = bot.handle.minGas();
                }
            } else {
                this.maxFeePerGas = macro.bn.zero;
                this.maxPriorityFeePerGas = macro.bn.zero;
                this.gasPrice = macro.bn.zero;
            }
        }
    }

    reset(){
        if(bot.handle.block.isEIP1559){
            //eip1559
            this.maxFeePerGas = bot.handle.minGas();
            this.maxPriorityFeePerGas = this.maxFeePerGas;
            this.gasPrice = undefined;
        } else {
            this.gasPrice = bot.handle.minGas();
            this.maxFeePerGas = undefined;
            this.maxPriorityFeePerGas = undefined;
        }
    }

    value(){
        if(bot.chain == macro.CHAIN.PLS || bot.chain == macro.CHAIN.MXC){
            let val = this.maxFeePerGas || this.gasPrice || bot.handle.minGas();
            return val;
        } else {
            let val = this.maxPriorityFeePerGas || this.gasPrice || bot.handle.minGas();
            if(bot.chain == macro.CHAIN.CRO){
                const gasMin = utils.parseUnits(bot.config.gasMin.toString(), 'gwei');
                if(gasMin.gt(val)) val = gasMin;
            } else if(bot.chain == macro.CHAIN.ARB){
                return utils.parseUnits('0.1','gwei');
            }
            return val;
        }
    }

    display(){
        let str = "";
        if(this.maxFeePerGas && this.maxPriorityFeePerGas) {
            str = `g:${Number(utils.formatUnits(this.maxPriorityFeePerGas, 'gwei')).toFixed(3)} | ${Number(utils.formatUnits(this.maxFeePerGas, 'gwei')).toFixed(3)}`;
        } else if(this.gasPrice){
            str = `g:${utils.formatUnits(this.gasPrice, 'gwei')}`;
        } else {
            str = `g: -`;
        }
        return str;
    }

    set(gas:BigNumber){
        if(this.gasPrice !== undefined){
            this.gasPrice = gas;
        } else {
            this.maxFeePerGas = gas;
            this.maxPriorityFeePerGas = gas;
        }
        return this;
    }

    setGasLimit(gasLimit:BigNumber){
        this.gasLimit = gasLimit;
        this.gasCost = gasLimit.mul(this.value());
        if(bot.chain == macro.CHAIN.ARB) this.gasCost = this.gasCost.mul(BigNumber.from(5));
        return this;
    }

    //计算gas消耗和转换成tokenIn的价格
    getGasCost(stableAddr:string){
        //if(this.gasCostTable[stableAddr]) return this.gasCostTable[stableAddr];

        const bEth = bot.token(bot.config.eth);
        const stable = bot.token(stableAddr);

        if(bot.config.eth == stableAddr || (bEth.decimals == stable.decimals && bEth.price == stable.price)){
           this.gasCostTable[stableAddr] = {bn: this.gasCost, num: bEth.toNum(this.gasCost)};
           
           return this.gasCostTable[stableAddr];
        }

        let gasCost = this.gasCost.mul(BigNumber.from((bEth.price * 10000).toFixed()));
        if(bEth.decimals > stable.decimals){
            gasCost = gasCost.div(10**(bEth.decimals - stable.decimals));
        } else if (bEth.decimals < stable.decimals){ //aurora的near是24位
            gasCost = gasCost.mul(10**(stable.decimals - bEth.decimals));
        }
        gasCost = gasCost.div((stable.price * 10000).toFixed());
        this.gasCostTable[stableAddr] = {bn: gasCost, num: stable.toNum(gasCost)};
        return this.gasCostTable[stableAddr];
    }

    sub(gas = utils.parseUnits('1','wei')){
        if(this.gasPrice){
            this.gasPrice = this.gasPrice.sub(gas);
        }
        if(this.maxFeePerGas && this.maxPriorityFeePerGas){
            this.maxFeePerGas = this.maxFeePerGas.sub(gas);
            this.maxPriorityFeePerGas = this.maxPriorityFeePerGas.sub(gas);
        }
        return this;
    }

    add(gas = utils.parseUnits('1','wei')){
        if(this.gasPrice){
            this.gasPrice = this.gasPrice.add(gas);
        }
        if(this.maxFeePerGas && this.maxPriorityFeePerGas){
            this.maxFeePerGas = this.maxFeePerGas.add(gas);
            this.maxPriorityFeePerGas = this.maxPriorityFeePerGas.add(gas);
        }
        return this;

    }

    applyGas(gasUseBn:BigNumber, whaleMaxFeePerGas?:BigNumber){
        if(!bot.handle.block.isEIP1559){
            this.gasPrice = gasUseBn;
            this.maxFeePerGas = undefined;
            this.maxPriorityFeePerGas = undefined;
        } else { 
            this.gasPrice = undefined;
            this.maxPriorityFeePerGas = gasUseBn;
            this.maxFeePerGas = (whaleMaxFeePerGas||this.maxFeePerGas||bot.handle.minGas()).add(utils.parseUnits(bot.config.gasDelta.toFixed(8), 'gwei'));
            if(this.maxFeePerGas?.lt(this.maxPriorityFeePerGas)) this.maxFeePerGas = this.maxPriorityFeePerGas.mul(macro.bn.microMore).div(macro.bn.h);
        }
    }

    bid(amount = 0){
        const {gasDelta, gasMax} = bot.config;
        const gas = this.value();
        const gasNum = Number(utils.formatUnits(gas, 'gwei'));
        //const highest = Number(utils.formatUnits(bot.handle.highestGas, 'gwei')); //当前区块的最高gas
        const delta = gasNum + gasDelta;

        //let gasUse = Math.min(Math.max(delta, highest+0.001333), gasMax);
        let gasUse = Math.min(delta, gasMax);
        let gasUseBn = utils.parseUnits(gasUse.toFixed(9), 'gwei');
        
        if(bot.chain == macro.CHAIN.ONE){
            gasUseBn = gas.add(utils.parseUnits('1.1', 'gwei'));
            let req2Gas = 0;
            if(amount > 130000){
                req2Gas = 59011.0000001;
            } else if(amount > 70000){
                req2Gas = 53012.0000001;
            } else if(amount > 40000){
                req2Gas = 40012.0000001;
            } else if(amount > 20000){
                req2Gas =  32011.23574001;
            } else if (amount > 8000){
                req2Gas =  12011.23574001;
            } else if ( amount > 5000){
                req2Gas = 5001.1203
            } else if( amount > 1000){
                req2Gas = 1002.2746
            }
            this.moreBigGas = utils.parseUnits( Math.max(req2Gas, gasUse).toFixed(9) , 'gwei');
        }

        this.applyGas(gasUseBn);
        //console.log(`    --- gas --  ${gasUse} / ${highest} /${gasNum}`);
        Lazy.ins().log1(`${macro.COLOR.BBlack}    --- gas --  ${gasUse} / ${0} /${gasNum}${macro.COLOR.Off}`);
        return this;
    }

    lessMore(trans?:ethers.providers.TransactionResponse){
        const {gasDelta, gasMax} = bot.config;
        
        let gasUse = (this.maxPriorityFeePerGas || this.gasPrice || bot.handle.minGas()).mul(macro.bn.lessMore).div(macro.bn.h);

        if(trans) {
            const bigGas = trans.maxPriorityFeePerGas || trans.gasPrice || bot.handle.minGas();
            if(gasUse.lte(bigGas)) gasUse = bigGas.add(utils.parseUnits(gasDelta.toFixed(8), 'gwei'));
        }
        this.applyGas(gasUse, trans?.maxFeePerGas);
        return this;
    }

    litterMore(trans?:ethers.providers.TransactionResponse){
        const {gasDelta, gasMax} = bot.config;
        
        let gasUse = (this.maxPriorityFeePerGas || this.gasPrice || bot.handle.minGas()).mul(macro.bn.littleMore).div(macro.bn.h);

        if(trans) {
            const bigGas = trans.maxPriorityFeePerGas || trans.gasPrice || bot.handle.minGas();
            if(gasUse.lte(bigGas)) gasUse = bigGas.add(utils.parseUnits(gasDelta.toFixed(8), 'gwei'));
        }
        this.applyGas(gasUse, trans?.maxFeePerGas);
        return this;
    }
    
    more(){
        const {gasDelta, gasMax} = bot.config;
        const gas = this.maxPriorityFeePerGas || this.gasPrice || bot.handle.minGas();
        const gasNum = Number(utils.formatUnits(gas, 'gwei'));
        let gasUse = utils.parseUnits(Math.min(gasNum+gasDelta, gasMax).toFixed(8), 'gwei');
        this.applyGas(gasUse);
        return this;
    }

    bigMore(trans?:ethers.providers.TransactionResponse){
        const {gasDelta, gasMax} = bot.config;
        let gasUse = this.maxPriorityFeePerGas || this.gasPrice || bot.handle.minGas();
        gasUse = gasUse.mul(macro.bn.bigMore).div(macro.bn.h);
        this.applyGas(gasUse, trans?.maxFeePerGas);
        return this;
    }

    clone(){
        const newGas = new MixGas();
        newGas.gasPrice = this.gasPrice;
        newGas.maxFeePerGas = this.maxFeePerGas;
        newGas.maxPriorityFeePerGas = this.maxPriorityFeePerGas;
        newGas.type = this.type;
        return newGas;
    }

    //把gas数据附加到tx上
    attach(tx : ethers.providers.TransactionRequest | UnsignedTransaction){
        //console.log("mixGas: ", this);
        if(this.maxFeePerGas && this.maxPriorityFeePerGas){
            tx.maxFeePerGas = this.maxFeePerGas;
            tx.maxPriorityFeePerGas = this.maxPriorityFeePerGas;
            tx.type = 2;
        } else {
            tx.gasPrice = this.gasPrice;
            tx.type = 0;
        }
        return this;
    }
} 

export class BaseSwapExData {
    data : SwapAmoutsOut = new SwapAmoutsOut();
    mixGas : MixGas = new MixGas();
    frontRun = false;
    whale: WhaleSwapData;
    whaleTrans : ethers.providers.TransactionResponse | undefined = undefined;

    operator?:string;
    operatorIndex = -1;
    nonce?:number;

    bid = false;
    bidTime = 0;

    onPending? : ()=>void;
    onFinish? : (receipt:ethers.providers.TransactionReceipt)=>void;
    onFail? : (e:any)=>void;
    
    constructor(input : {
        data : SwapAmoutsOut,
        whale?: WhaleSwapData,
        mixGas? : MixGas,
        bid?: boolean, //是否竞价
        frontRun?:boolean, //是否抢跑
        whaleTrans? : ethers.providers.TransactionResponse
    }){
        const {data, whale, bid, mixGas, frontRun, whaleTrans} = input;
        this.data = data;
        this.whale = whale || new WhaleSwapData();

        if(mixGas) this.mixGas = mixGas;
        if(bid) this.bid = bid;
        if(frontRun) this.frontRun = frontRun;
        if(whaleTrans) this.whaleTrans = whaleTrans;
    }
}

export class WalletData {
    bull:{[k:string]:string} = {};
    bear:{[k:string]:string} = {};
    pump:{[k:string]:string} = {};
    oracle:{[k:string]:string} = {};
}

export interface SwapResult {
    amountIn:BigNumber;
    amountOut:BigNumber;

    path:string[];
    tokenIn?:string, //如果是指定了tokenIn，则path表示的是pair

    to:string;
    deadline:BigNumber;
    exactIn:boolean;
    isEth:boolean;
}

export class SumToken {
    sum : {[k:string]:number} = {};

    add(addr:string, num:number){
        this.sum[addr] ??= 0;
        this.sum[addr] += num;
    }
    data(){
        return this.sum;
    }
}

class StableBalance {
    now = 0; //单一币种价格
    nowSum = 0; //累加其他token的价格
    init = -1;
}

export class StableBalanceSum {
    stableSum : {[addr:string]: StableBalance} = {};
    totalVal = new StableBalance();
    enableLogSum = true;

    async show(preStr = "", extra?:SumToken){
        Object.values(this.stableSum).forEach( _s => {
            _s.now = 0;
            _s.nowSum = 0;
        });

        if(extra){
            for(const [k,v] of Object.entries(extra.data())){
                this.stableSum[k] ??= new StableBalance();
                this.stableSum[k].nowSum += v;
            }
        }

        const {stableTokens} = bot.config;
        for(let i = 0; i < stableTokens.length; i++){
            const addr = stableTokens[i];
            let b = await bot.client.bull.getBalance(addr);
            this.stableSum[addr] ??= new StableBalance();
            this.stableSum[addr].now = b.num;
            this.stableSum[addr].nowSum += b.num;
            if(this.stableSum[addr].init == -1) this.stableSum[addr].init= this.stableSum[addr].nowSum;

        }


        this.totalVal.now = 0; 
        for(const [k,v] of Object.entries(this.stableSum)){
            const token = bot.token(k);
            this.totalVal.now += token.price * v.now;
            if(this.enableLogSum) Lazy.ins().logTs1(`${macro.COLOR.Cyan}****** ${bot.handle.blockNum} ${preStr} (${bot.config.tokens[k].name.padStart(12, " ")}) ${v.nowSum.toFixed(3)} sum: ${v.now.toFixed(3)}/${v.init.toFixed(3)}${macro.COLOR.Off}`);
            if(v.init == -1) v.init = v.now;
        }
        if(this.totalVal.init == -1) this.totalVal.init = this.totalVal.now;
        
        if(this.enableLogSum) Lazy.ins().logTs1(`${macro.COLOR.BCyan}****** ${bot.handle.blockNum} ${preStr} total values: $ ${this.totalVal.now.toFixed(2)} / ${this.totalVal.init.toFixed(2)}${macro.COLOR.Off}`);
        this.enableLogSum = false;
    }

    get(token:string){
        return this.stableSum[token] ? this.stableSum[token].now : 0;
    }

}

export default class DataType {
    static swapData2Serer(dataIn:SwapAmoutsOut){
        const dataOut = new ServerSwapData();
        dataOut.amountsIn = dataIn.amountsIn;
        dataOut.minOut = dataIn.outSlippage.bn;
        dataOut.pairs = dataIn.pairs;
        dataOut.sumIn = dataIn.in.bn;
        dataOut.tokenIn = dataIn.tokenIn;
        return dataOut;
    }

    static whale2Server(dataIn:WhaleSwapData){
        const dataOut = new ServerWhale();
        dataOut.addr = dataIn.addr;
        dataOut.amount = dataIn.amount;
        dataOut.isEth = dataIn.isEth;
        dataOut.isGreater = dataIn.isGreater;
        dataOut.token = dataIn.token;

        dataOut.spender = dataIn.spender;
        dataOut.spenderToken = dataIn.spenderToken;
        dataOut.spenderAmount = dataIn.spenderAmount;
        return dataOut;
    }

    static pumpEncodePacked(datas : ServerPumpData[], selector : string){
        let bs : string[] = [];
        datas.forEach(data=>{
            let str = utils.solidityPack(["uint8", "uint8", "uint8", "uint24", "uint88", "uint112"], [data.convertEth, data.tokenIn0or1, data.calc, data.gasLimit, data.cost, data.amountIn]);
            data.pairs.forEach( p =>{
                str += utils.solidityPack(["uint8","uint16","uint24","address"], [p.version, p.fee, p.fp, p.addr]).slice(2);
            });
            bs.push(str);
            //console.log(str);
        });
        const raw = bot.abiCoder.encode(["bytes[]"], [bs]);
        return selector + raw.slice(2);
    }

    static _old_pumpSafeReqEncode(tokenIn:string, tokenOut:string, amountIn:number, amountOutMin:number, pairs:string[]){
        let str = ["0x81768576"]; //pumpSafe
        str.push(this.hexAddress(tokenIn));
        str.push(this.hexAddress(tokenOut));
        str.push(this.hexBignumber(bot.token(tokenIn).toBigNumber(amountIn)));
        str.push(this.hexBignumber(bot.token(tokenOut).toBigNumber(amountOutMin)));
        str.push("00000000000000000000000000000000000000000000000000000000000000a0"); //固定最后一位
        const b = this.pairToServerDataBytesSafe(pairs);
        const padB = this.hexBytes(b);
        str.push(this.hexNumber(b.length/2));
        str.push(padB);
        return str.join('');
    }

    static hexAddress(addr:string){
        return addr.toLocaleLowerCase().replace('0x','').padStart(64,'0');
    }

    static hexPosition(index:number){
        return BigNumber.from(index*32).toHexString().replace('0x','').padStart(64,'0');
    }
    static hexNumber(num:number){
        return BigNumber.from(num).toHexString().replace('0x','').padStart(64,'0');
    }
    static hexBignumber(bn:BigNumber){
        return bn.toHexString().replace('0x','').padStart(64,'0');
    }

    static hexBytes(b:string){
        //补充到
        let step = Math.ceil(b.length / 64);
        return b.padEnd(64 * step, '0');
    }

    static dataViewer(str:string, hasSelector=true){
        if(hasSelector){
            console.log(`${str.slice(0,10)}`);
        }
        const delta = hasSelector ? 10 : 0;
        for(let i = 0; i < str.length / 64; i++){
            let a = str.slice(i*64+delta, i*64+64+delta);
            console.log(`[${i.toString().padStart(2, ' ')}] ${a}`);
        }
    }

    static findPathResultsToServerPumpData(paths:FindPathResult[], selector = "0xc480dd84") : FindPathResultsToServerPumpData{
        let totalGas = 0;
        let totalRewardValue = 0;

        let pumpServerDatas = paths.map(_p=>{
            const d = new ServerPumpData();
            d.convertEth = BigNumber.from(_p.convertEth);
            d.tokenIn0or1 = BigNumber.from(_p.tokenIn0or1);
            d.calc = BigNumber.from(_p.mevType);
            d.gasLimit = BigNumber.from(_p.gas);
            d.cost = _p.gasCost;
            
            if(_p.mevType !== macro.MEV_TYPE.DXDY){
                //console.log("d.amountIn = _p.amount; //固定值，不在线求极");
                d.amountIn = _p.amount; //固定值，不在线求极
            }
            
            totalGas += _p.gas;
            totalRewardValue += _p.rewardValue;
            
            for(let i = 0 ; i < _p.pairs.length; i++){
                const sd = new ServerPairData();
                const p2 = bot.oracleV2.data.pm.get(_p.pairs[i]);
                sd.addr = p2.address;
                sd.fp = BigNumber.from(p2.fp);
                sd.fee = BigNumber.from(_p.fees[i] || 0);
                sd.version = p2.version;
                d.pairs.push(sd);
            }
            return d;
        });
        return {
            totalGas : totalGas,
            totalRewardValue : totalRewardValue,
            data : pumpServerDatas,
            encodePackedData : this.pumpEncodePacked(pumpServerDatas, selector)
        }
    }

    static pairToServerData(addrs:string[], fees : number[]){
        return addrs.map((addr, index) => {
            const data = new ServerPairData();
            const { version, fp} = bot.oracleV2.data.pm.get(addr);
            data.addr = addr;
            data.fee = BigNumber.from(fees[index]);
            data.fp = BigNumber.from(fp);
            data.version = version;
            //console.log(`addr: ${addr}, fee0:${fee0Server.toNumber()}, fee1:${fee1Server.toNumber()}`)
            return data;
        });
    }

    static pairToServerDataBytes(addrs:string[], fees:number[]){
        let data = "";
        addrs.forEach((addr, index) =>{
            const {version, fp} = bot.oracleV2.data.pm.get(addr);
            data += BigNumber.from(version).toHexString().replace('0x','');
            data += BigNumber.from(fees[index]).toHexString().replace('0x','');
            data += BigNumber.from(fp).toHexString().replace('0x','');
            data += addr.toLocaleLowerCase().replace('0x','');
        });
        return data;
    }

    //只记录router的fee。用于探测f0和f1
    static pairToServerDataOnlyRouterFee(pairs:Pair[]){
        if(pairs.length == 0) return [];
        return pairs.map(pair => {
            const data = new ServerPairData();
            //const {version, fp} = bot.oracleV2.data.pm.get(addr);
            //console.log(bot.oracleV2.data.pm.get(addr));

            data.addr = pair.address;
            data.fee = BigNumber.from(0);
            data.fp = BigNumber.from(pair.fp);
            data.version = pair.version;
            //console.log(`addr: ${addr}, version:${data.version} fee0:${data.fee0.toNumber()}, fee1:${data.fee1.toNumber()}`)
            return data;
        });
    }

    //使用pumpSafe接口，忽略token自身的fee, //only router fee
    static pairToServerDataBytesSafe(addrs:string[]){
        let data = "";
        addrs.forEach(addr=>{
            const {version, fp} = bot.oracleV2.data.pm.get(addr);
            data += BigNumber.from(version).toHexString().replace('0x','');
            data += BigNumber.from(fp).toHexString().replace('0x','');
            data += BigNumber.from(fp).toHexString().replace('0x','');
            data += addr.toLocaleLowerCase().replace('0x','');
        });
        return data;
    }

    static pairToLocalData(hexEncodeData:string){
        const step = 54;
        const length = ~~(hexEncodeData.length/step);
        const pairs : string[] = [];
        for(let i = 0 ; i < length; i++){
            let str = hexEncodeData.slice(i*step,(i+1)*step);
            const version = BigNumber.from("0x" + str.slice(0,2));
            const fee0 = BigNumber.from("0x" + str.slice(2,8));
            const fee1 = BigNumber.from("0x" + str.slice(8,14));
            const addr = "0x" + str.slice(14,54);
            //console.log(str);
            //console.log(`${version.toNumber()} ${fee0.toNumber()} ${fee1.toNumber()} ${addr}`);
            pairs.push(addr);
        }
        return pairs;
    }

}