// 用于转发websocket
import { BigNumber, ethers, providers, utils } from "ethers";
import bot from "../../bot";
import { macro } from "../macro";
import Lazy from "../lazy/LazyController";
import tools from "../tools";
import { ProviderBase } from "./ProviderBase";


export class ProviderWsX extends ProviderBase {
    isListenLog = false;
    isListenPending = false;

    lastPendingBlock = 0;
    hasPending = true;
    reconnectTimeout : NodeJS.Timeout = setTimeout(() => {}, 0);

    checkSpeed = false;
    beginTime = 0;

    constructor(url: string, id:number, onlyBroadcast=false){
        super(url, id, onlyBroadcast);
        this.listen(false);
    }

    resetProvider(){
        //console.log(`#### (${this.id}) ${this.url} resetProvider`);
        this.provider.removeAllListeners();
        (this.provider as providers.WebSocketProvider)._websocket.terminate();
    }

    async updateMinGas(){
        //console.log("disable updateMinGas");
    }

    async listen(reconnect = true){
        if(reconnect){
            Lazy.ins().logTs1(`${macro.COLOR.Green}#### (${this.id}) (ProviderWsX) connecting to: ${this.url} ${this.onlyBroadcast ? "[onlyBroadcast]" : ""}${macro.COLOR.Off}`);
            this.provider = new ethers.providers.WebSocketProvider(this.url);
        }

        const p = this.provider as providers.WebSocketProvider;
        // websocket
        p._websocket.on('open', ()=>{
            Lazy.ins().logTs1(`${macro.COLOR.Green}#### (${this.id}) (ProviderWsX) ${this.url} opened.`);
            this.reconnectWalletAndContract();
            if(bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.UPDATE) {
                this.isReady = true;
            } else {
                //this.onOpen();
                this.subscribe();
            }
            bot.event.emit(macro.EVENT.WS_READY, this.id);
        });
        p._websocket.on('close', ()=>{
            Lazy.ins().logTs1(`${macro.COLOR.Red}#### (ProviderWsX) (${this.id}) ${this.url} The websocket connection was closed${macro.COLOR.Off}`);
            this.isReady = false;
            clearTimeout(this.reconnectTimeout);
            p.destroy().then(()=>{
                setTimeout(()=>{
                    this.reconnectTimeout = setTimeout(this.resetProvider.bind(this), 1000 * 10 * bot.config.blockTime); //10个区块没有收到block重启
                    bot.event.emit(macro.EVENT.WS_RECONNECT, this.id);
                    this.listen();
                }, 10000)
            });
        });
        /*
        p._websocket.on('message', (msg:string)=>{
            console.log(`${macro.LINUX_COLOR.Blue}#### message 11111${macro.LINUX_COLOR.Off}`, msg);
        });
        */

       //复写原来的方法
       /*
       p._websocket.onmessage = function (messageEvent:any) {
            console.log(`${macro.LINUX_COLOR.BYellow}#### message 3333 ${macro.LINUX_COLOR.Off}`, messageEvent.data);
            p._websocket.on('error', (e:any)=>{ console.log("websocket.on error: ", e); });
       }
       */

        //provider

    }

    async subscribe(){
        this.provider.on("block", (blockNumber :number)=>{
            //p._subscribe("newHeads", ["newHeads"], (result) => {
                bot.event.emit(macro.EVENT.WS_ON_BLOCK, blockNumber);
                //Lazy.ins().logTs(`block: ${blockNumber}`);
                this.checkSpeed = true;

                //if(bot.config.disableSubscribeLog && this.blockNum + 20 >= blockNumber) this.getlog(this.blockNum+1, blockNumber); //监听不超过20个block

                this.blockNum = blockNumber;
    
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = setTimeout(this.resetProvider.bind(this), 1000 * 10 * bot.config.blockTime); //10个块没有收到block重启

                if(blockNumber == 0) return; //没有同步上的时候blockNum是0

                if(this.hasPending){
                    this.lastPendingBlock = blockNumber;
                    this.hasPending = false;
                } else if(this.isListenPending) {
                    const length = blockNumber - this.lastPendingBlock;
                    if(length > bot.config.maxNoPendingBlock){
                        Lazy.ins().logTs1(`${macro.COLOR.Red}#### (${this.id}) ${this.url} no pending over 60 block  #############${macro.COLOR.Off}`);
                        this.hasPending = true;
                        this.lastPendingBlock = blockNumber;
                        this.resetProvider();
                    }
                }
        });
        if(bot.mode == macro.BOT_MODE.MEV) return;

        while(true){
            if(bot.oracleV2?.isReady){
                this.subscribeLog();
                break;
            }
            await tools.delay(3000);
        }
    }
    

    async subscribeLog(){
        //必须等oracle初始化后才有所有pair列表
        const MAX = 50000;
        for(let i = 0; i < bot.oracleV2.data.pm.data.size / MAX; i++){
            const p = [...bot.oracleV2.data.pm.data.keys()].slice(i*MAX, (i+1)*MAX);
            console.log(`subscribe log length: `, p.length);
            let filter = {
                address: p,
                topics : [
                    //utils.id("Sync(uint112 reserve0, uint112 reserve1)")
                    "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1",
                    //"0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822" //canto
                ]
            };
            (this.provider as providers.WebSocketProvider)._subscribe("logs", ["logs", filter], (result) => {
                this.hasPending = true;
                const {address, data, blockNumber, transactionHash} = result;
                const [r0,r1] = bot.abiCoder.decode(["uint112", "uint112"], data);
                bot.handle.onSync(address, r0, r1, transactionHash);
            });
            await tools.delay(5000);
        }
    }

    reconnectWalletAndContract(){
        let contratCount = 0;
        Object.values(this._contactList).forEach(c => contratCount+= Object.values(c).length);
        Lazy.ins().logTs1(`${macro.COLOR.Green}#### (${this.id}) ${this.url} [reconnect] wallet: ${Object.keys(this._walletList).length}, contract: ${contratCount}${macro.COLOR.Off}`);
        for(let [addr, w] of Object.entries(this._walletList)){
            w.connect(this.provider);
        }
        for(let [op, contracts] of Object.entries(this._contactList)){
            for(let c of Object.values(contracts)){
                c.connect(this._walletList[c.walletAddr].ins());
            }
        }
    }

    //连接成功
    async onOpen(){
        await tools.delay(2000);
        const startTime = Date.now(); // 记录开始时间
        const num = await this.provider.getBlockNumber(); // 获取当前gas价格
        const endTime = Date.now(); // 记录结束时间
        const duration = endTime - startTime; // 计算持续时间
        console.log(`${macro.COLOR.Green}#### (${this.id}) ${this.url} getBlockNumber: ${num} in ${duration} ms${macro.COLOR.Off}`);


        if(bot.chain == macro.CHAIN.PLS && duration > 45 && this.id == 0){
            console.log(`${macro.COLOR.BRed}response time is too long, reset provider in 10s...${macro.COLOR.Off}`);
            await tools.delay(10000);
            this.resetProvider();
            this.isReady = false;
            return;
        }
        

        this.isReady = true;
    }

    
}