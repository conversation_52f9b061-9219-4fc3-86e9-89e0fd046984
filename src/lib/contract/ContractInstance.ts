import { BigNumber, ethers, utils } from "ethers";
import DataType, { CheckMevsResultDesc, GetPairInfoResultDesc, MevPath, ServerFeedList, ServerPairData } from "../type/DataType";
import { Pair } from "../type/Pair";
import { macro } from "../macro";
import bot from "../../bot";

class CheckMevResult {
    gas = 0;
    fee0 : number[] = [];
    fee1 : number[] = [];
}

export default class ContractInstance {
    iBot = new ethers.utils.Interface(macro.abi.bot);

    async getPairReserves(pair:string){
        let version = macro.PAIR_VERSION.V2;
        let r0 : BigNumber;
        let r1 : BigNumber;
        let blockTimestampLast = 0;

        const data = await bot.provider().call({
            to:pair,
            data: "0x0902f1ac"
        });
        //console.log(data);
        try {
            const [_reserve0, _reserve1, _blockTimestampLast] = bot.abiCoder.decode(["uint", "uint", "uint"], data);
            //console.log(data);
            version = macro.PAIR_VERSION.V2;
            r0 = _reserve0;
            r1 = _reserve1;
            blockTimestampLast = (_blockTimestampLast as BigNumber).toNumber();
        } catch(e){
            //解析出错了，尝试是否v1
            const [_reserve0, _reserve1] = bot.abiCoder.decode(["uint", "uint"], data);
            version = macro.PAIR_VERSION.V1;
            r0 = _reserve0;
            r1 = _reserve1;
        }

        //console.log(`getPairReserves: pair${pair}, version:${version}, r0:${r0}, r1:${r1} ts:${blockTimestampLast}`);
        return {
            version: version,
            r0: r0,
            r1 : r1,
            blockTimestampLast : blockTimestampLast
        };
    }

    async batchCheckPairFee(inputs : Array<{pair:Pair, tokenIn:string, prePairs:Pair[]}>){
        if(inputs.length == 0) return [[]];
        try {
            return await this._batchCheckPairFee(inputs);
        } catch (e) {
            const results : number[][] = [];
            for(const input of inputs){
                try {
                    results.push((await this._batchCheckPairFee([input]))[0]);
                } catch (e){
                    console.log("[batchCheckPairFee fail]");
                    console.log(e);
                    results.push([99998, 99998]);
                }
            }
            return results;
        }
    }

    private async _batchCheckPairFee(inputs : Array<{pair:Pair, tokenIn:string, prePairs:Pair[]}>) {
        const datas = inputs.map(_i =>{
            return {
                pair: DataType.pairToServerDataOnlyRouterFee([_i.pair]),
                prePair : DataType.pairToServerDataOnlyRouterFee(_i.prePairs),
                tokenIn : _i.tokenIn
            }
        });
        const raw = await bot.provider().call({
            from : await bot.getbotOwner(),
            to : bot.config.bot.address,
            data : this.iBot.encodeFunctionData("batchCheckPairFee", [ datas ])
        });
        const res = this.iBot.decodeFunctionResult("batchCheckPairFee", raw)[0] as Array<{fee0:BigNumber, fee1:BigNumber}>;
        return res.map(_r => {
            return [_r.fee0.toNumber(), _r.fee1.toNumber()]
        });
    }

    async batchGetPairInfo(addrs : string[]){
        try {
            let res = await this._batchGetPairInfo(addrs);
            return res;
        } catch(e) {
            //console.log(e);
            const results : Pair[] = [];
            for(const m of addrs){
                try {
                    let res = await this._batchGetPairInfo([m]);
                    results.push(res[0]);
                } catch(e) {
                    console.log("[ERR] batchGetPairInfo: ", m);
                    results.push(new Pair());
                }
            }
            return results;
        }
    }
    //批量获取pair的info，也可用于批量跟新reserves
    private async _batchGetPairInfo(addrs : string[]){
        const data = this.iBot.encodeFunctionData("batchGetPairInfo", [addrs]);
        const raw = await bot.provider().call({
            data : data,
            to : bot.config.bot.address,
            from : await bot.getbotOwner()
        });
        const res = this.iBot.decodeFunctionResult("batchGetPairInfo", raw);
        const pairs : Pair[] = [];
        (res[0] as GetPairInfoResultDesc[]).forEach(_r=>{
            const {token0, token1, r0, r1, d0, d1, lastUpdate, stable, s0, s1, addr} = _r;
            //console.log(res[0][0]);
            let p = new Pair();
            p.address = addr;
            p.token0 = token0;
            p.token1 = token1;
            p.blockTimestampLast = (lastUpdate as BigNumber).toNumber();
            p.token0Symbol = s0;
            p.token1Symbol = s1;

            //忽略不能分割bignumber的数量，基本是假币
            if(r0.lt(BigNumber.from(10))){
                p.reserve0 = 0;
            } else {
                p.reserve0 = Number(utils.formatUnits(r0, (d0 as BigNumber).toNumber()));
            }
            if(r1.lt(BigNumber.from(10))){
                p.reserve1 = 0;
            } else {
                p.reserve1 = Number(utils.formatUnits(r1, (d1 as BigNumber).toNumber()));
            }
            p.stable = stable;
            pairs.push(p);
        });
        return pairs;
    }

    //检查mev path是否可以跑，并且返回对应的fees和gas消耗
    async batchCheckMev(mevs : MevPath[]){
        try {
            let res = await this._batchCheckMev(mevs);
            return res;
        } catch(e) {
            //console.log(e);
            const results : CheckMevResult[] = [];
            for(const m of mevs){
                try {
                    let res = await this._batchCheckMev([m]);
                    results.push(res[0]);
                } catch(e) {
                    console.log("[ERR] batchCheckMev: ", m.pairs.join(", "));
                    results.push(new CheckMevResult());
                }
            }
            return results;
        }
    }

    private async _batchCheckMev(mevs : MevPath[]){
        const d = mevs.map(_m => {
            const firstP = bot.oracleV2.data.pm.get(_m.pairs[0]);
            return  {
                tokenIn0or1:_m.s0 == firstP.token0,
                pairs: DataType.pairToServerDataOnlyRouterFee(_m.pairs.map(_p=> bot.oracleV2.data.pm.get(_p)))
            }
        });
        //console.log(d);

        const data = this.iBot.encodeFunctionData("batchCheckMev", [d]);
        const raw = await bot.provider().call({
            data : data,
            to : bot.config.bot.address,
            from : await bot.getbotOwner()
        });
        const res = this.iBot.decodeFunctionResult("batchCheckMev", raw);
        return (res[0] as CheckMevsResultDesc[]).map(_r => {
            const r = new CheckMevResult();
            r.gas  = _r.gas.toNumber();
            r.fee0 = _r.fee0.map( _f0 => _f0.toNumber());
            r.fee1 = _r.fee1.map( _f1 => _f1.toNumber());
            return r;
        });
    }

}