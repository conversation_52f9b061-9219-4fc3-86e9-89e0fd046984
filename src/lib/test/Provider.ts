import { timeStamp } from "console";
import { Contract, ethers, utils } from "ethers";
import tools from "../tools";

enum LINUX_COLOR {
    // Reset
    Off='\x1b[0m',       // Text Reset

    // Regular Colors
    Black='\x1b[0;30m',        // Black
    Red='\x1b[0;31m',          // Red
    Green='\x1b[0;32m',        // Green
    Yellow='\x1b[0;33m',       // Yellow
    Blue='\x1b[0;34m',         // Blue
    Purple='\x1b[0;35m',       // Purple
    Cyan='\x1b[0;36m',         // <PERSON><PERSON>='\x1b[0;37m',        // White

    // Bold
    BBlack='\x1b[1;30m',       // Black
    BRed='\x1b[1;31m',         // Red
    BGreen='\x1b[1;32m',       // Green
    BYellow='\x1b[1;33m',      // Yellow
    BBlue='\x1b[1;34m',        // Blue
    BPurple='\x1b[1;35m',      // Purple
    BCyan='\x1b[1;36m',        // Cyan
    B<PERSON>hite='\x1b[1;37m',       // White

    // Underline
    UBlack='\x1b[4;30m',       // Black
    URed='\x1b[4;31m',         // Red
    UGreen='\x1b[4;32m',       // Green
    UYellow='\x1b[4;33m',      // Yellow
    UBlue='\x1b[4;34m',        // Blue
    UPurple='\x1b[4;35m',      // Purple
    UCyan='\x1b[4;36m',        // Cyan
    UWhite='\x1b[4;37m',       // White

    // Background
    On_Black='\x1b[40m',       // Black
    On_Red='\x1b[41m',         // Red
    On_Green='\x1b[42m',       // Green
    On_Yellow='\x1b[43m',      // Yellow
    On_Blue='\x1b[44m',        // Blue
    On_Purple='\x1b[45m',      // Purple
    On_Cyan='\x1b[46m',        // Cyan
    On_White='\x1b[47m',       // White

    // High Intensity
    IBlack='\x1b[0;90m',       // Black
    IRed='\x1b[0;91m',         // Red
    IGreen='\x1b[0;92m',       // Green
    IYellow='\x1b[0;93m',      // Yellow
    IBlue='\x1b[0;94m',        // Blue
    IPurple='\x1b[0;95m',      // Purple
    ICyan='\x1b[0;96m',        // Cyan
    IWhite='\x1b[0;97m',       // White

    // Bold High Intensity
    BIBlack='\x1b[1;90m',      // Black
    BIRed='\x1b[1;91m',        // Red
    BIGreen='\x1b[1;92m',      // Green
    BIYellow='\x1b[1;93m',     // Yellow
    BIBlue='\x1b[1;94m',       // Blue
    BIPurple='\x1b[1;95m',     // Purple
    BICyan='\x1b[1;96m',       // Cyan
    BIWhite='\x1b[1;97m',      // White

    // High Intensity backgrounds
    On_IBlack='\x1b[0;100m',   // Black
    On_IRed='\x1b[0;101m',     // Red
    On_IGreen='\x1b[0;102m',   // Green
    On_IYellow='\x1b[0;103m',  // Yellow
    On_IBlue='\x1b[0;104m',    // Blue
    On_IPurple='\x1b[0;105m',  // Purple
    On_ICyan='\x1b[0;106m',    // Cyan
    On_IWhite='\x1b[0;107m',   // White
}

function testSpeedWs(url:string, pending = ""){
    console.log(`connecting to: ${url}`);
    const color = LINUX_COLOR;

    const provider = new ethers.providers.WebSocketProvider(url);
    provider._websocket.on('open', ()=>{
        console.log(" ws open");
    });
    if(pending == ""){
        console.log("listing pending...");
        provider.on('pending', async (tx:any)=>{
            console.log(`${tools.now().str} ${color.Blue}${tx}${color.Off}`);
            const beginTime = new Date().getTime();
            const trans = await provider.getTransaction(tx);
            const speed = new Date().getTime() - beginTime;
            console.log(`${tools.now().str} ${trans ? trans.from : ""} -> ${trans ? trans.to : ""} ${color.Green}${speed.toFixed()}ms${color.Off}`);
           // await trans.wait();
           // console.log(`${color.BBlack}${tools.now().str} finish: ${trans ? trans.from : ""} -> ${trans ? trans.to : ""} ${color.Off}`);
        });
    }

    let currentBlock = 0;
    provider.on("block", async (blockNumber :number)=>{
        console.log(`${tools.now().str} block: `, blockNumber);
        //test get
        const beginTime = new Date().getTime();
        let gas = await provider.getGasPrice();
        const speed = new Date().getTime() - beginTime;
        console.log(`get gas speed: ${speed}ms`);

        if(currentBlock == 0) currentBlock = blockNumber - 1;
        let logs = await provider.getLogs({fromBlock:currentBlock, toBlock:blockNumber});
        console.log(`logs : ${logs.length}`);
        currentBlock = blockNumber;
    });


    /*
    let abi = new ethers.utils.AbiCoder();
    let filter = {
        address : ["******************************************", "******************************************", "******************************************", "******************************************"],
        topics : [
            //utils.id("Sync(uint112 reserve0, uint112 reserve1)")
            "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1"
        ]
    }
    provider._subscribe("logs", ["logs", filter], (result) => {
        console.log("--------------------------------");
        console.log("logs : ", result);
        const {address, data, blockNumber} = result;
        const [r0,r1] = abi.decode(["uint112", "uint112"], data);
        console.log(address,r0,r1);
    });
    */

    /*
    provider._subscribe("newHeads", ["newHeads"], async (result) => {
        //console.log(result);
        console.log("block : ", result.Number, "timestamp: ", result.Timestamp);
        const beginTime = new Date().getTime();
        let gas = await provider.getGasPrice();
        const speed = new Date().getTime() - beginTime;
        console.log(`get gas speed: ${speed}ms`);
    });
    */
}

async function testSpeedHttp(url:string){
    let provider = new ethers.providers.JsonRpcProvider(url)
    const beginTime = new Date().getTime();
    const gas = await provider.getGasPrice();
    const speed = new Date().getTime() - beginTime;
    console.log(`gas: ${utils.formatEther(gas)}, spd: ${speed}ms`);
    let currentBlock = 0;
    provider.on("block", async (blockNumber :number)=>{
        console.log(`${tools.now().str} block: `, blockNumber);
        //test get
        const beginTime = new Date().getTime();
        let gas = await provider.getGasPrice();
        const speed = new Date().getTime() - beginTime;
        console.log(`get gas speed: ${speed}ms`);

        if(currentBlock == 0) currentBlock = blockNumber - 1;
        let logs = await provider.getLogs({fromBlock:currentBlock, toBlock:blockNumber});
        console.log(`logs : ${logs.length}`);
        currentBlock = blockNumber;
    });
}


function testFilter(){
    let url = "wss://xlayertestws.okx.com";
    let abi = new ethers.utils.AbiCoder();

    const provider = new ethers.providers.WebSocketProvider(url);
    provider._websocket.on('open', ()=>{
        console.log("[testFilter] ws open");
    });
    provider.on("block", (blockNumber :number)=>{
        console.log(`[testFilter] ${tools.now().str} block: `, blockNumber);
    });

    console.log(`listen: ${utils.id("Sync(uint112 reserve0, uint112 reserve1)")}`);
    
    let filter = {
        address : ["******************************************", "******************************************"],
        topics : [
            //utils.id("Sync(uint112 reserve0, uint112 reserve1)")
            "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1"
        ]
    }
    provider._subscribe("logs", ["logs", filter], (result) => {
        console.log("--------------------------------");
        console.log("logs : ", result);
        const {address, data, blockNumber} = result;
        const [r0,r1] = abi.decode(["uint112", "uint112"], data);
        console.log(address,r0,r1);
    });
}

async function testNewPendingTransactionFilter(){
    let url = "wss://exchainws.okex.org";
    const provider = new ethers.providers.WebSocketProvider(url);
    provider._subscribe("newPendingTransactions", ["newPendingTransactions"], (e)=>{
        console.log(e);
    });
    
}

function testCfx(url:string, pending = ""){
    console.log(`connecting to: ${url}`);
    const color = LINUX_COLOR;

    const provider = new ethers.providers.WebSocketProvider(url);
    provider._websocket.on('open', ()=>{
        console.log(" ws open");
    });
    console.log("listing pending...");
    
    provider.on('pending', async (tx:any)=>{
        console.log(` ${color.Blue}${tx}${color.Off}`);
        const beginTime = new Date().getTime();
        const trans = await provider.getTransaction(tx);
        const speed = new Date().getTime() - beginTime;
        console.log(` ${trans ? trans.from : ""} -> ${trans ? trans.to : ""} ${color.Green}${speed.toFixed()}ms${color.Off}`);
    });

    provider.on("block", async (blockNumber :number)=>{
        console.log(`${tools.now().str} block: `, blockNumber);
        //test get
        const beginTime = new Date().getTime();
        let gas = await provider.getGasPrice();
        const speed = new Date().getTime() - beginTime;
        console.log(`get gas speed: ${speed}ms`);
    });
    

    /*
    provider._subscribe("newHeads", ["newHeads"], (result) => {
        console.log(result);
        console.log("block : ", result.Number, "timestamp: ", result.Timestamp);
    });
    */
}

const arg = process.argv.splice(2);

//testNewPendingTransactionFilter();
//testFilter();



if(arg[0]){
    if(arg[0].includes("ws")) testSpeedWs(arg[0], arg[1]);
    if(arg[0].includes("http")) testSpeedHttp(arg[0]);
} else {
    console.log(" ##### error websocket url");
}

