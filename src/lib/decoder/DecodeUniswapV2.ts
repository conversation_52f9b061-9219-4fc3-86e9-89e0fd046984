import { BigNumber, ethers } from "ethers";
import bot from "../../bot";
import { SwapResult } from "../type/DataType";
import Lazy from "../lazy/LazyController";
import { macro } from "../macro";

class UniswapV2Func {
    set = 0;
    type = 0;

    name = "";
    exactIn = false;
    isEth = false;
}

export default class DecodeUniswapV2 {

    decodeSwap(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let decode;
        try {
            decode = bot.abiCoder.decode(selector.type == 0 ? ["uint256","address[]","address","uint256"] : ["uint256", "uint256", "address[]", "address", "uint256"], "0x" + data.slice(10, data.length));
        } catch(e){
            Lazy.ins().log1(`${macro.COLOR.BRed}[handle] decode error: ${trans.from} ${macro.COLOR.Off}`);
            return;
        }
        let amountIn, amountOut;
        if(selector.type == 0){
            amountIn = trans.value;
            amountOut = decode[0] as BigNumber;
        } else {
            const [a, b] = [decode[0] as BigNumber, decode[1] as BigNumber];
            if(selector.exactIn){
                [amountIn, amountOut] = [a,b];
            } else {
                [amountIn, amountOut] = [b,a];
            }
        }
        const result : SwapResult = {
            amountIn : amountIn,
            amountOut : amountOut,
            path : decode[1+selector.type],
            to : decode[2+selector.type],
            deadline : decode[3+selector.type],
            exactIn : selector.exactIn,
            isEth : selector.isEth
        };
        return result;
    }

    decodeAddLiq(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let tokenA = "";
        let tokenB = "";
        let amountADesired : BigNumber;
        let amountBDesired : BigNumber;
        let decode;
        if(selector.isEth){
            //addLiquidityETH(address token, uint amountTokenDesired, uint amountTokenMin, uint amountETHMin, address to, uint deadline)
            decode = bot.abiCoder.decode(["address", "uint256", "uint256", "uint256", "address", "uint256"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = bot.config.routers[trans.to as string].eth || bot.config.eth;
            amountADesired = decode[2];
            amountBDesired = trans.value;
        } else {
            //addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline)
            decode = bot.abiCoder.decode(["address", "address", "uint256", "uint256", "uint256", "uint256", "address", "uint256"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = decode[1];
            amountADesired = decode[2];
            amountBDesired = decode[3];
        }
        return {
            tokenA: tokenA,
            tokenB: tokenB,
            amountADesired : amountADesired,
            amountBDesired : amountBDesired
        }
    }

    decodeRemoveLiq(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let tokenA = "";
        let tokenB = "";
        let liquidity : BigNumber;
        let decode;
        if(selector.isEth){
            //removeLiquidityETH(address token, uint liquidity, uint amountTokenMin, uint amountETHMin, address to, uint deadline)
            decode = bot.abiCoder.decode(["address", "uint256", "uint256", "uint256", "address", "uint256"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = bot.config.routers[trans.to as string].eth || bot.config.eth;
            liquidity = decode[1];
        } else {
            //removeLiquidity(address tokenA, address tokenB, uint liquidity, uint amountAMin, uint amountBMin, address to, uint deadline)
            decode = bot.abiCoder.decode(["address", "address", "uint256", "uint256", "uint256", "address", "uint256"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = decode[1];
            liquidity = decode[2];
        }
        return {
            tokenA: tokenA,
            tokenB: tokenB,
            liquidity : liquidity,
        }
    }

    //"swapExactETHForTokens(uint256,address[],address,uint256,bool[])",
    //"swapExactETHForTokensSupportingFeeOnTransferTokens(uint256,address[],address,uint256,bool[])",
    //"swapExactTokensForTokens(uint256,uint256,address[],address,uint256,bool[])",
    //"swapExactTokensForETH(uint256,uint256,address[],address,uint256,bool[])",
    //"swapExactTokensForTokensSupportingFeeOnTransferTokens(uint256,uint256,address[],address,uint256,bool[])",
    //"swapExactTokensForETHSupportingFeeOnTransferTokens(uint256,uint256,address[],address,uint256,bool[])",

    decodeSwapMute(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let decode;
        try {
            decode = bot.abiCoder.decode(selector.type == 0 ? ["uint256","address[]","address","uint256","bool[]"] : ["uint256", "uint256", "address[]", "address", "uint256","bool[]"], "0x" + data.slice(10, data.length));
        } catch(e){
            Lazy.ins().log1(`${macro.COLOR.BRed}[handle] decode error: ${trans.from} ${macro.COLOR.Off}`);
            return;
        }
        let amountIn, amountOut;
        if(selector.type == 0){
            amountIn = trans.value;
            amountOut = decode[0] as BigNumber;
        } else {
            const [a, b] = [decode[0] as BigNumber, decode[1] as BigNumber];
            if(selector.exactIn){
                [amountIn, amountOut] = [a,b];
            } else {
                [amountIn, amountOut] = [b,a];
            }
        }
        const result : SwapResult = {
            amountIn : amountIn,
            amountOut : amountOut,
            path : decode[1+selector.type],
            to : decode[2+selector.type],
            deadline : decode[3+selector.type],
            exactIn : selector.exactIn,
            isEth : selector.isEth
        };
        return result;
    }

    //"addLiquidityETH(address,uint256,uint256,uint256,address,uint256,uint256,bool)",
    //"addLiquidity(address,address,uint256,uint256,uint256,uint256,address,uint256,uint256,bool)",
    decodeAddLiqMute(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let tokenA = "";
        let tokenB = "";
        let amountADesired;
        let amountBDesired;
        let decode;
        if(selector.isEth){
            //addLiquidityETH(address token, uint amountTokenDesired, uint amountTokenMin, uint amountETHMin, address to, uint deadline, uint feeType, bool stable)
            decode = bot.abiCoder.decode(["address", "uint256", "uint256", "uint256", "address", "uint256", "uint256", "bool"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = bot.config.routers[trans.to as string].eth || bot.config.eth;
            amountADesired = decode[2];
            amountBDesired = trans.value;
        } else {
            //addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint feeType, bool stable)
            decode = bot.abiCoder.decode(["address", "address", "uint256", "uint256", "uint256", "uint256", "address", "uint256", "uint256", "bool"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = decode[1];
            amountADesired = decode[2];
            amountBDesired = decode[3];
        }
        return {
            tokenA: tokenA,
            tokenB: tokenB,
            amountADesired : amountADesired,
            amountBDesired : amountBDesired
        }
    }

    //"removeLiquidity(address,address,uint256,uint256,uint256,address,uint256,bool)",
    //"removeLiquidityETH(address,uint256,uint256,uint256,address,uint256,bool)",
    //"removeLiquidityETHSupportingFeeOnTransferTokens(address,uint256,uint256,uint256,address,uint256,bool)",
    decodeRemoveLiqMute(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let tokenA = "";
        let tokenB = "";
        let liquidity;
        let decode;
        if(selector.isEth){
            //removeLiquidityETH(address token, uint liquidity, uint amountTokenMin, uint amountETHMin, address to, uint deadline, bool stable)
            decode = bot.abiCoder.decode(["address", "uint256", "uint256", "uint256", "address", "uint256", "bool"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = bot.config.routers[trans.to as string].eth || bot.config.eth;
            liquidity = decode[1];
        } else {
            //removeLiquidity(address tokenA, address tokenB, uint liquidity, uint amountAMin, uint amountBMin, address to, uint deadline)
            decode = bot.abiCoder.decode(["address", "address", "uint256", "uint256", "uint256", "address", "uint256", "bool"], "0x" + data.slice(10, data.length));
            tokenA = decode[0];
            tokenB = decode[1];
            liquidity = decode[2];
        }
        return {
            tokenA: tokenA,
            tokenB: tokenB,
            liquidity : liquidity,
        }
    }

    decodeKubDK(rawData:string, trans:ethers.providers.TransactionResponse){
        const decode = bot.abiCoder.decode(["address[]","bytes[]","bytes32[]"], "0x" + rawData.slice(10, rawData.length));
        const trueRouters = (decode[0] as string[]);
        const datas = (decode[1] as string[]);
        let data = "";
        let trueRouter = "";
        let selector : UniswapV2Func | undefined;
        for(let i = 0 ; i < datas.length; i++){
            const s = macro.uniswapV2FuncMap[datas[i].slice(2,10)];
            if(s){
                selector = s;
                data = datas[i];
                trueRouter = trueRouters[i];
                break;
            }
        }
        if(!selector) return;
        

        console.log("selectorid: ", data.slice(2,10));
        console.log(selector);
        switch(selector.set){
            case 1: //流动性逻辑
                if(selector.type == 0){
                    const result = this.decodeAddLiq(selector, data, trans);
                    return {action:0, result:result, router:""};
                    //this.addLiquidity(trans, result.tokenA, result.tokenB, result.amountADesired, result.amountBDesired);
                } else if(selector.type == 1){
                    const result = this.decodeRemoveLiq(selector, data, trans);
                    return {action:1, result:result, router:""};
                    //this.removeLiquidity(trans, result.tokenA, result.tokenB, result.liquidity);
                }
                break;
            case 2: //swap逻辑
                const result = this.decodeSwap(selector, data, trans);
                //if(result) this.swapFilter(to, trans, result, websocketId);
                return {action:2, result: result, router:""};
            case 101: //TODO: kub liq
                break;
            case 102: //kub dk swap
                const resultDK = this.decodeNextSwap(selector, data, trans);
                return {action:2, result: resultDK, router:trueRouter};
        }
    }

    //0x472b43f3 swapExactTokensForTokens(uint256,uint256,address[],uint256)
    //0x42712a67 swapTokensForExactTokens(uint256,uint256,address[],uint256)
    decodePls(rawData:string){
        const decode1 = bot.abiCoder.decode(["uint256", "bytes[]"], "0x" + rawData.slice(10, rawData.length));
        const datas = (decode1[1] as string[]);
        const results : SwapResult[] = [];
        for(let i = 0 ; i < datas.length; i++){
            let data = datas[i];
            const selector = macro.uniswapV2FuncMap[datas[i].slice(2,10)];
            //console.log(`decodePls -> selector id: ${datas[i].slice(2,10)}`);
            if(!selector) continue;
            const trueRouter = selector.name == "swapExactTokensForTokensV2" ? "******************************************" : "******************************************";
            //console.log(data);
            //0xab0acea4 swapExactTokensForTokensV2(uint256,uint256,address[],address)
            const decode = bot.abiCoder.decode(["uint256", "uint256", "address[]", "uint256"], "0x" + data.slice(10, data.length));
            let amountIn, amountOut;
            const [a, b] = [decode[0] as BigNumber, decode[1] as BigNumber];
            if(selector.exactIn){
                [amountIn, amountOut] = [a,b];
            } else {
                [amountIn, amountOut] = [b,a];
            }

            results.push({
                amountIn : amountIn,
                amountOut : amountOut,
                path : decode[1+selector.type],
                to : trueRouter, //应该是发起人
    
                deadline : BigNumber.from(0),
                exactIn : selector.exactIn,
                isEth : selector.isEth
            });
        }
        return results;
    }
    //0x4ef346a0      uint router, address[] path, uint s1, uint s2, address ref, uint amountIn
    //0x61f398f1(Eth) uint router, address[] path, uint s1, uint s2, address ref, uint amountIn
    // 0: plsV1, 1:plsV2
    decodePls2(rawData:string){
        const decode = bot.abiCoder.decode(["uint256", "address[]", "uint256", "uint256", "address", "uint256"], "0x" + rawData.slice(10, rawData.length));
        const routerIndex = (decode[0] as BigNumber).toNumber();
        const paths : string[] = decode[1];
        const amoutIn : BigNumber = decode[5];
        const results : SwapResult[] = [];
        let trueRouter = "";
        if(routerIndex == 0){
            trueRouter = "******************************************";
        } else if(routerIndex == 1){
            trueRouter = "******************************************";
        } else {
            console.log("unknow router: ", routerIndex);
            return results;
        }
        results[0] = {
            amountIn: amoutIn,
            amountOut : macro.bn.zero,
            path : paths,
            to : trueRouter,
            deadline : BigNumber.from(0),
            exactIn : true,
            isEth : rawData.includes("61f398f1")
        }
        return results;
    }
    //TODO: token path
    //https://www.oklink.com/cn/oktc/tx/0xd0f787af0644bd62994dc87b101b6c40c69415203b5601176275ec2c0ad393b5
    decodeOkxSingle(rawData:string){
        //address tokenIn，uint amoutnIn, uint amountOutMin, address[] pairs
        //tokenin如果是eth则=0
        let decode: ethers.utils.Result;
        try {
            decode = bot.abiCoder.decode(["bytes32", "uint256", "uint256", "bytes32[]"], "0x" + rawData.slice(10, rawData.length));
        } catch(e){
            Lazy.ins().log1(`${macro.COLOR.BRed}[handle] decode error: ${rawData} ${macro.COLOR.Off}`);
            return;
        }
        console.log("decode:", decode);
        let tokenIn = '0x' + (decode[0] as string).slice(26);
        if(tokenIn == "******************************************") tokenIn = bot.config.eth;

        const amountIn = decode[1] as BigNumber;
        const amountOutMin = decode[2] as BigNumber;
        const pairs = (decode[3] as string[]).map(_d=>{
            //const uint1 = _d.slice(0, 18); // '0x8000000000000000'
            //const uint2 = _d.slice(18, 26); // '3b6d0340'
            const addressHex = _d.slice(26); // 'a75bd9f086bbc1168b01fd5e750986b5170c2b26'
            return '0x' + addressHex;
        });

        return {
            amountIn : amountIn,
            amountOut : amountOutMin,
            path : pairs,
            tokenIn : tokenIn,

            deadline : BigNumber.from(0),
            exactIn : true,
            isEth : false
        } as SwapResult;
    }

    //https://eco.elastos.io/address/******************************************?tab=read_write_contract
    decodePga(selector:UniswapV2Func, rawData:string, trans:ethers.providers.TransactionResponse){
        let amountIn : BigNumber = macro.bn.zero;
        let amountOut : BigNumber = macro.bn.zero;
        let tokena:string = "";
        let tokenb:string = "";
        let to:string = "";
        let deadline:BigNumber = macro.bn.zero;
        let decode;
        switch(selector.type){
            case 0: //FastSwapEthToToken(amountOutMin, token, to, deadline)
                decode = bot.abiCoder.decode(["uint256", "address", "address", "uint256"], "0x" + rawData.slice(10, rawData.length)); 
                amountIn = trans.value;
                amountOut = decode[0] as BigNumber;
                tokena = bot.config.eth;
                tokenb = decode[1];
                to = decode[2];
                deadline = decode[3];
                break;
            case 1: //FastSwapTokenToEth(amountIn, amountOutMin, token, to, deadline)
                decode = bot.abiCoder.decode(["uint256", "uint256", "address", "address", "uint256"], "0x" + rawData.slice(10, rawData.length));
                amountIn = decode[0] as BigNumber;
                amountOut = decode[1] as BigNumber;
                tokena = decode[2];
                tokenb = bot.config.eth;
                to = decode[3];
                deadline = decode[4];
                break;
            case 2: //FastSwapTokenToToken(amountIn, amountOutMin,tokena,tokenb,to,deadline)
                decode = bot.abiCoder.decode(["uint256", "uint256", "address", "address","address", "uint256"], "0x" + rawData.slice(10, rawData.length));
                amountIn = decode[0] as BigNumber;
                amountOut = decode[1] as BigNumber;
                tokena = decode[2];
                tokenb = decode[3];
                to = decode[4];
                deadline = decode[5];
                break;
        }

        const result : SwapResult = {
            amountIn,
            amountOut,
            path : [tokena, tokenb],
            to,
            deadline,
            exactIn : selector.exactIn,
            isEth : selector.isEth
        };
        return result;
    }


    decodeNextSwap(selector:UniswapV2Func, data:string, trans:ethers.providers.TransactionResponse){
        let decode;
        try {
            //["uint256", "uint256", "address[]", "address", "uint256"]
            decode = bot.abiCoder.decode(["uint256", "uint256", "address[]", "uint256", "string", "address"], "0x" + data.slice(10, data.length));
        } catch(e){
            Lazy.ins().log1(`${macro.COLOR.BRed}[handle] decode error: ${trans.from} ${macro.COLOR.Off}`);
            return;
        }
        let amountIn, amountOut;
        if(selector.type == 0){
            amountIn = trans.value;
            amountOut = decode[0] as BigNumber;
        } else {
            const [a, b] = [decode[0] as BigNumber, decode[1] as BigNumber];
            if(selector.exactIn){
                [amountIn, amountOut] = [a,b];
            } else {
                [amountIn, amountOut] = [b,a];
            }
        }
        const result : SwapResult = {
            amountIn : amountIn,
            amountOut : amountOut,
            path : decode[1+selector.type],
            to : decode[4+selector.type],
            deadline : decode[2+selector.type],
            exactIn : selector.exactIn,
            isEth : selector.isEth
        };
        return result;
    }

}