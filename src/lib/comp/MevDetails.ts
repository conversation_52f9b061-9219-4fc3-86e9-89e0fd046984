import bot from "../../bot";
import Lazy from "../lazy/LazyController";
import { macro } from "../macro";
import { FindPathResult, MevPath } from "../type/DataType";

class MevPathWithVal {
    mev : MevPath = new MevPath();
    sum = 0;
    vals : RewardLog[] = [];
}

class RewardLog{
    ts = 0;
    val = 0;
}

export default class MevDetails {
    //recordMin: 记录最近n分钟的tx， -1:记录所有价值，不记录详情
    recordMin = -1;

    // 1.缓存当前区块已经发送出去的mevpath
    private pathSignatureCache = new Set<string>(); // 存储已执行的路径特征值
    private cachedBlockNum = -1; // 缓存的区块号

    //2.记录mev价值
    public topMevsPathKey : string[] = [];
    public mevMap : { [k:string]: MevPathWithVal } = {};

    uptime = 0;
    bootTime = Math.floor((new Date()).getTime() / 1000);

    total_cost = 0;
    total_reward = 0;

    latest_reward = 0;

    constructor(recordMin = -1){
        this.recordMin = recordMin;
    }


    /** 1.
     * 生成 FindPathResult 的唯一特征值
     * 特征值由当前区块号和 pairs 数组内容组成
     * @param path FindPathResult 对象
     * @returns 唯一特征值字符串
     */
    private generatePathSignatureWithBlock(path: FindPathResult): string {
        const blockNum = bot.handle.blockNum;
        const pairsStr = path.pairs.join(',');
        return `${blockNum}_${pairsStr}`;
    }


    /** 1.
     * 检查并更新缓存，清理过期的缓存数据
     */
    private updateCache(): void {
        const currentBlockNum = bot.handle.blockNum;

        // 如果区块号发生变化，清空所有缓存
        if (this.cachedBlockNum !== currentBlockNum) {
            this.pathSignatureCache.clear();
            this.cachedBlockNum = currentBlockNum;
            //Lazy.ins().logTs1(`${macro.COLOR.BBlack}[Cache] trash Block changed to ${currentBlockNum}, cache cleared${macro.COLOR.Off}`);
        }
    }

    /** 1.
     * 过滤掉已经缓存的路径，返回需要执行的路径
     * @param paths 原始路径数组
     * @returns 过滤后的路径数组
     */
    filterCachedPaths(paths: FindPathResult[]): FindPathResult[] {
        this.updateCache();

        const filteredPaths: FindPathResult[] = [];
        const newSignatures: string[] = [];

        for (const path of paths) {
            const signature = this.generatePathSignatureWithBlock(path);

            // 如果缓存中不存在该特征值，则添加到结果中
            if (!this.pathSignatureCache.has(signature)) {
                filteredPaths.push(path);
                newSignatures.push(signature);
            }
        }

        // 将新的特征值添加到缓存中
        newSignatures.forEach(sig => this.pathSignatureCache.add(sig));

        if (paths.length > filteredPaths.length) {
            Lazy.ins().logTs1(`${macro.COLOR.Yellow}[Cache] total:${this.pathSignatureCache.size} Filtered ${paths.length - filteredPaths.length} cached paths, ${filteredPaths.length} remaining${macro.COLOR.Off}`);
        }

        return filteredPaths;
    }

    /** 1.
     * 获取当前缓存状态信息
     * @returns 缓存状态对象
     */
    getCacheStatus(): { cacheSize: number; currentBlock: number; cachedBlock: number } {
        return {
            cacheSize: this.pathSignatureCache.size,
            currentBlock: bot.handle.blockNum,
            cachedBlock: this.cachedBlockNum
        };
    }

    /** 1.
     * 手动清理缓存（用于测试或特殊情况）
     */
    clearCache(): void {
        this.pathSignatureCache.clear();
        this.cachedBlockNum = -1;
        Lazy.ins().logTs1(`${macro.COLOR.BYellow}[Cache] Manually cleared cache${macro.COLOR.Off}`);
    }


    // 2.
    // 创建路径的标准化表示，用于去重
    // 将路径转换为字符串，并确保正向和反向路径有相同的表示
    normalizePathKey(pairs: string[]): string {
        // 创建正向和反向路径的字符串表示
        const forwardKey = pairs.join("->");
        const reverseKey = [...pairs].reverse().join("->");
        
        // 返回字典序较小的作为标准化表示
        return forwardKey < reverseKey ? forwardKey : reverseKey;
    }


    /** 2.
     * 
     */
    addMevRecord(m : MevPath, reward:number){
        const key = this.normalizePathKey(m.pairs);
        this.mevMap[key] ??= { mev : m, sum : reward, vals:[] };
        this.mevMap[key].sum += reward;
        if(this.recordMin > 0){
            this.mevMap[key].vals.push({
                ts : (new Date()).getTime(),
                val : reward
            });
        }
        this.total_reward += reward;
    }


    generateTopMevs(){
        //删除mevMap中ts时间大于半小时的vals项目，并从新计算sum = vals的总和，如果vals是空则删除该mevMap项目
        const now = (new Date()).getTime();
        if(this.recordMin > 0){
            Object.values(this.mevMap).forEach(v=>{
                v.vals = v.vals.filter(v=> now - v.ts < 1000 * 60 * this.recordMin); //30分钟
                v.sum = v.vals.reduce((a, b) => a + b.val, 0);
                if(v.vals.length == 0){
                    delete this.mevMap[this.normalizePathKey(v.mev.pairs)];
                }
            });
        }

        this.uptime = Math.floor(now / 1000) - this.bootTime;

        //根据mevMap的rewardSum生成topMevsPathKeys
        this.topMevsPathKey = Object.values(this.mevMap).sort((a, b) => b.sum - a.sum).map(v=>this.normalizePathKey(v.mev.pairs)).slice(0, 10);
    }

    updateUptime(){
        this.uptime = Math.floor((new Date()).getTime() / 1000) - this.bootTime;
    }

    displayTopMevs(data : MevPathWithVal[], detail = false){
        data.forEach((item, index) => {
            const { mev, sum: val } = item;
            
            // 获取路径上的代币符号
            const pairAddrs = mev.pairs;
            const tokenOuts: string[] = [];
            
            // 构建tokenOuts数组，模拟getAmountsOutAndUpdateCache的逻辑
            if (pairAddrs.length > 0) {
                const firstPair = bot.oracleV2.getPairByAddr(pairAddrs[0]);
                tokenOuts[0] = mev.s0; // 起始代币
                
                // 遍历每一对，确定输出代币
                for (let i = 0; i < pairAddrs.length; i++) {
                    const pair = bot.oracleV2.getPairByAddr(pairAddrs[i]);
                    const tokenIn = tokenOuts[i];
                    tokenOuts[i + 1] = tokenIn === pair.token0 ? pair.token1 : pair.token0;
                }
            }
            
            // 构建代币路径字符串
            let tokenPathStr = "";
            for (let z = 0; z < tokenOuts.length; z++) {
                const token = bot.token(tokenOuts[z]);
                tokenPathStr += `${token.symbol}(${mev.weight.toFixed(3)}) ${z === tokenOuts.length - 1 ? "" : "-> "}`;
            }
            
            // 打印路径信息
            const { Green, BBlack, Off } = macro.COLOR;
            console.log(`${BBlack}(${index+1}) ${Green}${tokenPathStr} ${BBlack} $ ${val.toFixed(3)}`);
            if(detail) {
                console.log(`${BBlack}${mev.pairs.join(" -> ")}`);
                mev.pairs.forEach(p=>{
                    const pE = bot.oracleV2.getPairByAddr(p);
                    const token0 = bot.token(pE.token0);
                    const token1 = bot.token(pE.token1);
                    console.log(`${token0.symbol}: ${pE.reserve0.toFixed(6)} | ${token1.symbol}: ${pE.reserve1.toFixed(6)}`);
                })
                // 打印交易对的库存信息
                console.log(`Gas: ${mev.gas}, Type: ${mev.type}${Off}`);
            } else {
            }
        });
    }

    formatUpTime(){
        //把runtime的秒数转化成00:00:00
        let hour = Math.floor(this.uptime / 3600).toString().padStart(2, '0');
        let min = Math.floor((this.uptime % 3600) / 60).toString().padStart(2, '0');
        let sec = (this.uptime % 60).toString().padStart(2, '0');
        return `${hour}:${min}:${sec}`;
    }

}