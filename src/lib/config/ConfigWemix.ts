import { macro } from "../macro";
import Config from "./Config";

export default class ConfigWemix extends Config {
    mainnet = {
        data : "https://api.wemix.com",
        wss : ["wss://ws.wemix.com"] //ms-jp
    };
    blockTime = 1;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    gasMin = 100.0001;
    gasDelta = 0.01;
    gasMax = 200;
    feedAutoTimeHour = 0.5; //单位小时
    onlyActivePair = false;

    feedMin = 1;
    feedMax = 1.5;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 10;
    //securityLevel = 1;

    bot = {
        active:true,
        crazy:false,
        address : "******************************************", //0412 + 0715
        gasMax : 200,
    }
    pump = {active:true, gasMax:200, countMin:1, countMax:3, rewardMin: 0.002}
    trash = {active:true, bid:true, delay:100, rewardMin: 0.001}

    tokens = {
        "******************************************" : {name:"wwemix", ignore:true, keep:10, price:0.3},
        "******************************************" : {name:"wemix$", ignore:true, cex:true },
    };
    tokenBlackList = {}

    routers = {
        "0x80a5A916FB355A8758f0a3e47891dc288DAC2665" : { name:"wemix", fee:99750},
        "0x662b1d3ae00f81a303e9bc79ca17435f14315984" : { name:"u1", fee:99750},
        //"0x164056263dBB527D06B008F263780fC8f1Bd22F5" //Konverter_StableSwapHelper
        //"0x20fDb3F41371Ec0834a11DaFcDb9ACF5157236C4" : { name:"play"},
        "0x3512996D9e9BaCb77348eEE2bffCD3369761d302" : { name:"u2", fee:99750},
        "0x0BAfd384f221B0FFD6309E2FEF1B3165860cd59a" : {name:"u3", fee:99750},
    };

    gasWatch = [
        "0x71e4529B273debA8a897367CD61Ce1B90ff1b88a",  //大户1
        "0xca5021bf2514e5cdacf46870c2953b7771f2d552",  //大户2
    ]
    blackListPair = [];

    blackList = [
        /*
        "0x0e7f3632cb7348cc421558843bc7b665ddd0bf9d",
        "0xdd109271962cc2826bca116d84ee67cef8f2dac2",
        "0x5b06b2fdb5a2bf7681163b12f4a9f31f6f4a0544",
        "0x011ea91baace7aea56da07a397e69a249e742a18",
        "0x0e7f3632cb7348cc421558843bc7b665ddd0bf9d",
        "0x48ed99efbe4126bdd64b171f235f6734d2827059",
        "0xdd109271962cc2826bca116d84ee67cef8f2dac2",
        "0xaec42c222e3d1538f43311d7c73007a4185d187b",
        "0x2aad6745abdf7dec11b94528180f861302bc33e9",
        "0x38e0f6778008ad786c2310568e7f5d74c08941f1",
        "0xbdc059808a6c7065b7ca686e9a3fdb52c076e4a7",
        "0x8e52493fe965eb55233163f8f9da81f38d6affdd",
        "0x48f890231e153eb7b164978ce6e2459da5d8d5e9",
        "0x0292e4d76361b8781f8fc04119a06774d3ce3216",
        "0x81bf552f9fc83e88012d6c3ab84cf1946bc55fd0",
        "0x7ac6d25fd5e437cb7c57aee77ac2d0a6cb85936c",        
        "0x797e89f90ea24e2bae27464bce2eaa9805684473",
        "0x2a75f1d955d973264246f7612650517807d89988",
        "0xbba91848bd2d7ee848edb108e93518389ff6fce1",
        "0xa56248497f903c5cc2ddcccb9af514867a6199bf",
        "0x9d67ed92dcfa510f852f9691ae3678c33cfaa85c",
        "0x15af2a0bbcfbe97bd2e12c277cfba82221f23b47",
        "0xac6bfda5420f462133399a6da952efa279e6580b",
        "0x1B5544bE359A71fA78fF91D33e7E5169B299a7B8", //残渣
        */
    ]

    blackListPump = [
    ]
}