import Config from "./Config";

export default class ConfigEla extends Config {
    mainnet = {
        //https://docs.glidefinance.io/resources/rpc-endpoints

        //hk : 230 ~ 1000ms
        //jp : 300 ~ 800ms
        //sg : 162 ~ 391ms
        //vg : 450 ~ 1800ms
        //de : 300 ~ 1300ms
        data : "https://api.elastos.io/eth",
        // https://api.elastos.io/esc
        // https://api.elastos.io/eth  asia
        //wss : ["ws://127.0.0.1:28546", "txpool|https://api.elastos.io/eth"]
        wss : ["txpool|https://api.elastos.io/eth"],
        //wss : ["ws://127.0.0.1:28546"]
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************"];
    gasMin = 2;
    gasMax = 1000;
    feedAutoTimeHour = 3;

    feedMin = 0.3;
    feedMax = 0.4;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 10;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115
    }
    pump = {active:true, gasMax:1000, countMin:3, rewardMin: 0.01, maxPump:15}
    trash = {active:true, bid:true, delay:500, rewardMin:0.01}

    tokens = {
        "******************************************" : {name:"wela", ignore:true, keep:0.1, isEth:true, price:3},
    };

    routers = {
        "******************************************" : { name: "glide", fee:99750 },
        "******************************************" : { name: "elk" },
        "******************************************" : { name: "tok" },
        
    };

    gasWatch = [
        "******************************************",
        "******************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}