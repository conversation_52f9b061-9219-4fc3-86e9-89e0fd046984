import Config from "./Config";

export default class ConfigEco extends Config {
    mainnet = {
        //https://docs.glidefinance.io/resources/rpc-endpoints

        //hk : 230 ~ 1000ms
        //jp : 300 ~ 800ms
        //sg : 162 ~ 391ms
        //vg : 450 ~ 1800ms
        //de : 300 ~ 1300ms
        data : "https://api.elastos.io/eco",
        // https://api.elastos.io/esc
        // https://api.elastos.io/eth  asia
        //wss : ["ws://127.0.0.1:28546", "txpool|https://api.elastos.io/eth"]
        wss : ["txpool|https://api.elastos.io/eco"]
    };
    blockTime = 3;
    eth = "******************************************";
    stableTokens = [
        "******************************************",
        "******************************************"
    ];
    gasMin = 5;
    gasMax = 200;
    feedAutoTimeHour = 1;

    feedMin = 1.8;
    feedMax = 2;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 7;

    bot = {
        active:false,
        crazy:false,
        address : "******************************************", //1115
        sharingan : true,
    }
    pump = {active:true, gasMax:1000, countMin:1, rewardMin: 0.003, maxPump:15}
    trash = {active:true, bid:true, delay:100, rewardMin:0.003}

    tokens = {
        "******************************************" : {name:"wela", ignore:true, keep:0.1, isEth:true, price:2},
        "******************************************" : {name:"btcd", ignore:true, keep:0.1, price:1},
        "******************************************" : {name:"usdt", ignore:true, keep:0.1, price:1},
    };

    routers = {
        "******************************************" : { name: "pga" },
        "******************************************" : { name: "pga2"},
        //"******************************************" : { name:"stable" },
        
    };

    gasWatch = [
        "******************************************"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}