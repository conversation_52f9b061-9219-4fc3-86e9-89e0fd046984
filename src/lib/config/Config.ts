import { macro } from "../macro";

export default class Config {
    mainnet : { wss:string[], data:string } = { wss:[], data:""};

    eth = "";
    stableTokens : string[] = [];
    onlyActivePair = false; //只缓存活跃的pair，减少内存消耗和监听的pair数量

    gasMin = 30;
    gasDelta = 0.1;
    gasMax = 301;

    feedMin = 1.8;
    feedMax = 2;
    feedPumpMulti = 0.3; //pump的填充gas百分比
    feedAutoTimeHour = -1;

    maxLp = 3;

    blockTime = 3; //每个区块的时间 必须大于60(时间上报的间隔是~~60/blockTime, 大于60会计算错误)
    maxNoPendingBlock = 60;

    maxOperator = 10; //操作员数量
    securityLevel = 10; //最高1级，最低不限
    updateAllPairsDelay = 0; //首次启动app更新所有pair的间隔时间 ms
    disableSubscribeLog = false; //关闭订阅，针对太多pair或者是provider不支持订阅的情况

    filterCachedPaths = true; //在trash中过滤重复请求

    tokens : {[k:string]: {
        name : string,
        isEth? : boolean, //是否gas
        maxLp?:number,
        max? : number,
        limit? : number,
        keep? : number,
        whiteList?: boolean, //bull在大宗卖出之后是否补仓
        ignore?:boolean, //忽略不进行交易，只参与path
        lazy?:boolean,
        cex?:boolean, //是否有中心化交易所的竞争机器人
        price?:number, //token的usd价值
        stable?:string, //指定stableToken
        eq?:string[], //等价货币，搬砖时候首尾可以用等价货币替换
    }} = {};
    tokenBlackList : {[k:string]: string} = {}

    bot  : { address:string, active:boolean, sharingan?:boolean, crazy?:boolean } = { address: macro.zeroAddress, active:false, crazy:false, sharingan: false};
    bot2 : { address:string, active:boolean } = { address: macro.zeroAddress, active:false };
    pump : { 
        active:boolean,
        gasMax:number,
        rewardMin?:number,
        countMin:number,
        maxPump?:number
    } = { active:false, gasMax:100, rewardMin:0, countMin:1, maxPump:6};
    trash : {
        active:boolean,
        gasMin?:number,    //最小gas
        rewardMin?:number, //最小收益

        bid?:boolean,      //是否竞价
        bidMinUsd?:number, //超过这个价格启动竞价

        //是否盲拍竞价, 让利的百分比0~1, l1Rpc:获取l1的gasPrice
        l2? : {
            bid : boolean,  //是否盲拍
            payout: number, //盲拍的出让利润百分比 1=100%
            l1GasRpc:string //获取l1的gas
        },

        delay:number,      //延时多少毫秒执行计算, 0:收到sync立刻计算
    } = { active:false, bid:false, delay:1000 };

    junk : { active:boolean, delay_ms:number, maxPair : number } = { active:false, delay_ms: 20 * 1000, maxPair:4};

    eoaRouter : { active:boolean, router:string, opKey:string } = { active:false, router: "", opKey:""} //独立路由模式，适用于只有一个router的链，所有token和pair都默认没有fee，所有交易走router

    routers: {[address:string] : {
        name:string,
        eth?:string,     //router指定的eth
        skipUpdate?:boolean, //不需要更新的router
        type?:macro.ROUTER_TYPE //router类型,
        
        fee?:number,
        stableFee?:number,   //用于v2StableRouter
        volatileFee?:number, //用于v2StableRouter
        factory?:string,     //自定义factory地址
    }} = {};

    gasWatch : Array<string> = [];

    whiteListPair : {[address:string]:{fee0:number, fee1:number}} = {};
    whiteListMev : {[mev:string]:boolean} = {};

    blackList : Array<string> = []; //TODO: 去除一般blacklist
    blackListPump : Array<string> = [];

    pumpGasBunner : Array<string> = [];
}