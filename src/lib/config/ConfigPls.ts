import { macro } from "../macro";
import Config from "./Config";

export default class ConfigPls extends Config {
    mainnet = {
        data : "https://rpc.pulsechain.com",
        //data : "https://rpc-pulsechain.g4mm4.io",
        //wss://rpc.pulsechain.com, //e
        //wss://rpc-pulsechain.g4mm4.io //nl, de, mi
        //wss://pulsechain.publicnode.com
        //wss://evex.cloud/pulsews
        //aws-sg 90ms
        //gc-io 20ms
        //gc-or 64ms
        //gc-vg 13ms
        //aws-ca 25ms
        //aws-ca-u 50ms
        //wss : ["safe|wss://rpc.pulsechain.com"],
        //wss : ["safe|wss://rpc-pulsechain.g4mm4.io","safe|wss://rpc.pulsechain.com"],
        //wss : ["safe|wss://rpc-pulsechain.g4mm4.io"], //nl, de, mi
        //wss : ["safe|wss://rpc.pulsechain.com", "wss://rpc-pulsechain.g4mm4.io"], //aws-sg, aws_ir, aws_oh //de ir
        //wss : ["https://pulsechain.publicnode.com"],
        //wss : ["https://rpc-pulsechain.g4mm4.io"],
        //wss : ["https://pulsechain.publicnode.com"],
        wss : ["https://rpc.pulsechain.com"],
    };
    blockTime = 10;
    eth = "******************************************";
    stableTokens = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************"
    ];
    gasMin = 2.5;
    gasDelta = 200;
    gasMax = 1500000000;
    feedAutoTimeHour = 1; //单位小时
    onlyActivePair = false;
    disableSubscribeLog = true;

    feedMin = 3000000;
    feedMax = 4000000;
    feedPumpMulti = 1; //pump的填充gas百分比

    maxOperator = 20;
    securityLevel = 1;

    bot = {
        active:false,
        crazy:false,
        sharingan: true,
        //address : "******************************************", //0412 + 0715
        address : "******************************************", //1115
        gasMax : 1500000000,
    }
    pump = {active:false, gasMax:200000000, countMin:1, countMax:3, rewardMin: 0.002}
    trash = {active:true, bid:false, delay:500, rewardMin: 0.05, bidMinUsd: 0.1 }

    eoaRouter = { //目前需要先手动approve一下router
        active:true,
        router: "******************************************",
        opKey:"81f27085106a5ca7bfa29695ad6bce3e904fc62bd655fce05464d03280fb36a9",
    }

    tokens = {
        "******************************************" : {name:"wpls", ignore:true, price:0.00005, isEth:true, eq:["******************************************"]},
        "******************************************" : {name:"weth", ignore:true, price:0.00005, isEth:true, eq:["******************************************"]},

        "******************************************" : {name:"dai", ignore:true, cex:true, },
        "******************************************" : {name:"usdc", ignore:true, cex:true, keep:20, eq:["******************************************"] }, //d6
        "******************************************" : {name:"usdt", ignore:true, cex:true, keep:20,  eq:["******************************************"] }, //d6
    };
    tokenBlackList = {}

    routers = {
        "******************************************" : { name:"u1", fee:99750 },
        "******************************************" : { name:"easy", fee:99720},
        "******************************************" : { name:"prate", type: macro.ROUTER_TYPE.V2_EOA}, //eoa router
        "******************************************" : { name: "pls", fee:99710 },
        "******************************************" : { name: "plsV2", fee:99710 },
        
        //"0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D" : { name:"u2", }, //主网旧router
        //"0xd9e1ce17f2641f24ae83637ab66a2cca9c378b9f" : { name:"u3", }, //旧router
        "0xd7877F3e5064F03e5327f9B1F245a0BD073B74C3" : { name:"u3", },
        "0x6055048abED3128B6c8c926Af2f275055F43a6fc" : { name:"u4", },
        "0x028C4B45a21DAA0714110B52F6541dAdd343bf6c" : { name:"u5", fee:99750},
        "0xFc38E937425673180bdA44aB9f2b4875cf4845b4" : { name:"zeros", fee:99750,},
        //"0x49b9009a62f921C58307e342546e8ab5C2138f05" : { name:"fire" }, //不是v2
        "0x370d160992C8C48BCCFcf009f0c9db9d00574eF7" : { name:"fire", type: macro.ROUTER_TYPE.V2_STABLE },
        //"0xa11aA626E637df91f3ccd4f795a3d07a3dFaF00e" : {name:"interM", }, //魔改v2, wrap plsV1和plsV2
        
        "0xc40cE31d9bcBe2edFbD30D0a7503f6C663b69877" : { name:"multi", skipUpdate:true }, //x聚合1
        "0xa619F23c632CA9f36CD4Dcea6272E1eA174aAC27" : { name:"multi2", skipUpdate:true }, //x聚合2
        "0x0Ce61DC009669C4691491263dCa7c30209924Ed3" : { name:"smart", skipUpdate:true }, //聚合3
        "0x68b3465833fb72a70ecdf485e0e4c7bd8665fc45" : { name:"smart2", skipUpdate:true }, //聚合4
        "0xDA9aBA4eACF54E0273f56dfFee6B8F1e20B23Bba" : {name:"x", skipUpdate:true},
        "0x607015EC03b0e2300520175a231c03155b8E1A48" : { name: "go", skipUpdate:true}, //go聚合3
    
        //"0x68b3465833fb72a70ecdf485e0e4c7bd8665fc45" : { name:"未知"}
        "******************************************" : { name:"nine" },
        "******************************************" : { name:"u6", fee:99750},

        //"******************************************" : { name:"u"},
        //"******************************************" : { name:"multi"}
        "******************************************" : { name: "spark" }, //有white list, 99700:99870
        //"******************************************" : { name: "smar", eth:"******************************************"}, //old eth
        //"******************************************" : { name:"vault"},
        "******************************************" : {name:"dex"},

        "******************************************" : {name:"u7"}, 
        "******************************************" : {name:"u8"},
        //"******************************************" : {name:"u9"}, //old v2, 17w pair

    };

    gasWatch = [
        "******************************************", //大户
        "******************************************", //大户2
        "******************************************", //搬砖
        "******************************************", //套娃
        "******************************************", //大套娃
        "******************************************", //搬砖
        "******************************************", //套娃1
        "******************************************", //套娃2
        "******************************************", //搬砖,新大户
        "0x5de5891ce56e7c764f8674e1c6f1ad533ed22b26", //搬砖
        "0x792c229e7539112191E69449bb6345852ba5ee61", //搬砖
        "0xB7ADeefde3fD02ceB334c5aeA55a56bf6F0d2284", //大户

        //"0x1234597c1a4336ec7519e68edba541a0f460c032", //router
        //"0x66e08198b5366fc0cde5fd78cc7b7686e554586f", //router
        "0xb10F745eb42437322cF6A54308B1cE0693dbacE9", //86f的搬砖

        "0xA595732AD0C848C07bF2b7C408b573aAa8BdBf62", //波段大户
        "0x22C0087056d5bfCAAa1D9B953cDCc3536Db0Aa75", //大鳄，持有10m资产
        "0xc762541E073D5F2EdDEd2b129E6b4B88126A79aB",
        "0x08d814734cceE1651fCAa376C87516D9c880369c",
        "0x84e3BC740d02C9C7CF09eF7482aeA0708baA25B7",
        "0x97375272f98400F1ddE3Ef432bcE221F8Eb09910",
        "0xa8e9a0f0da7933fd531244dda2618ba9da797ed0", //大户

        "0x5687398f7ca651eb6856423b7aaebfc62524780e", //router搬砖
        "0x97ee4ed562c7ed22f4ff7dc3fc4a24b5f0b9627e", //稳定币搬砖
        "0x089D24564186Acd4948d92eBED7f64Ef431834a2",
        
    ]
    blackListPair = [];

    //old, 准备去除
    blackList = [
        "0x5ea38cb7aab1927db0db8864360b0b15eabb4114",
        "0x7b846253d147885d41addf58e348d6677f9d81c0"
    ]

    blackListPump = [
        "0x5ea38cb7aab1927db0db8864360b0b15eabb4114",
        "0x7b846253d147885d41addf58e348d6677f9d81c0",
        "0xc886d97f6b788dd1b0fda2cb5011a7a40b8fb593",
        "0xab59c2efb76b76769c75a20d486e471d26507f0d",
        "0x838ff4d731a707c7f8c2be1165bd6ad89719dd20",
        "0xbfa9d07beacb458a4ef35c27ecaa50a333cbb65e",
        "0x9f5e93609504d1fea60b7e8e5a6c61b1b7ff7c1a",
        "0x47cae49f2a7235412aedc2653dfc8a4d38448ed6",
        "0xd5296bddbb575b401f836615af711e3dbbc6d2fb",
        "0xcc3a368569987ed44d3fa3cebfd8e17eec1fe97b",
        "0x1c8a7b4a73ef70809140369bc3ddcc9e4ea4e322",
        "0x06148cf3e04b2bccb79ce1f27d272674e23cfa74",
        "0x6d66a25d7203a6014e88d924d88cc3fbddb337b7",
        "0xb4d492451bbf9e83c9867c3e3fd87050fea93d35",
        "0xdaf4dde26f4bfe6374b92c85dddcc6e1aec9a3b6",
        "0xc76cf28c02d217a22271d4c8173a9e7a4fdd6e85",
        "0x56d7bbe7cb60ef35d15c892cfd7031671c51cf86",
        "0x62e0135c670b1ae94a03afc9826b28404ad17109",
        "0xa46939494a4ebba2e77805771968d8e10893850d",
        "0x2d4c702440095919e24dc6b54c360b39442a3485",
        "0x011ec597c0fbf7acf02b9fa878700d641895d73f",
        "0xf0cfa097dd55b01d6da76dbe4ee1e80e902f6702",
        "0x9f713685b8400ae5fcc297b9113e942ce2cdf121",
        "0x3bc450de20794a1b2bd6564412f4af5f9586115d",
        "0x5e11f7bb7c112f5c7f0d4bd0881e7efe84aff8bb",

        "0x66e08198b5366fc0cde5fd78cc7b7686e554586f", //忽略其他router机器人
        "0xcf1978cf90b629c23a6d59649faede6cd7e5d28f", //忽略自己的eoa机器人
        "0x6df8ce66fc716f95715c38361ced5c1d7c5de8d0",
    ]
}