use std::{str::FromStr, sync::Arc};

use alloy::primitives::Address;

use crate::{strategy::{pump::Pump, trash::Trash}, vira::Vira, CONFIG};

pub mod pump;
pub mod trash;

pub struct Strategy {
    pub trash : Trash,
    pub pump : Pump,
}

impl Strategy {
    pub fn new() -> Strategy {
        let trash_op : Vec<Address> = CONFIG.operators[0].iter().map(|f| Address::from_str(&f.0).unwrap()).collect();

        let st = Strategy { 
            trash : Trash::new(trash_op),
            pump : Pump::new()
        };
        st
    }

}