use std::{collections::HashMap, path::Path, env};

use colored::Colorize;

use eyre::{Result, eyre};

use duct::cmd;

/// 远程主机配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct Host {
    /// 主机名称
    pub name: String,
    /// IP地址
    pub ip: String,
    /// SSH端口
    pub port: u16,
    /// PEM密钥文件名（如果为空则使用密码认证）
    pub pem: String,
    /// 用户名
    pub user: String,
    /// 是否使用代理连接
    pub use_proxy: bool,

    _local_linux_base_path : String,
    _local_data_base_path : String,
    _remote_base_path : String,
}

impl Host {
    pub fn new(name: &str, ip: &str, port: u16, pem: &str, user: &str) -> Self {
        Self {
            name: name.to_string(),
            ip: ip.to_string(),
            port,
            pem: pem.to_string(),
            user: user.to_string(),
            use_proxy: false,
            _local_linux_base_path : "~/Script/rust/bera/target/x86_64-unknown-linux-musl/release/bera".to_string(),
            _local_data_base_path : "~/Sscript/rust/bera/data".to_string(),
            _remote_base_path : "~/temp".to_string(),
        }
    }

    /// 设置是否使用代理
    pub fn with_proxy(mut self, use_proxy: bool) -> Self {
        self.use_proxy = use_proxy;
        self
    }

    /// 展开路径中的波浪号(~)为用户主目录
    fn expand_tilde(path: &str) -> String {
        if path.starts_with("~/") {
            if let Ok(home) = env::var("HOME") {
                return path.replacen("~", &home, 1);
            }
        }
        path.to_string()
    }

    /// 构建SSH连接字符串
    fn build_ssh_connection(&self) -> String {
        let pem_option = if !self.pem.is_empty() {
            format!(" -i '~/Script/remote/aws/{}'", self.pem)
        } else {
            String::new()
        };

        let proxy_option = if self.use_proxy {
            " -o ProxyCommand='ssh -W %h:%p bwg'"
        } else {
            ""
        };

        format!("{}@{} -p {}{}{}",
                self.user, self.ip, self.port, pem_option, proxy_option)
    }

    /// 执行SSH命令
    pub fn ssh(&self, commands: &[&str]) -> Result<String> {
        let connection = self.build_ssh_connection();
        let command_str = commands.join(" && ");
        
        // 打印完整的格式化后的命令语句
        let full_command = format!("ssh {} {}", connection, command_str);
        println!("{} 执行命令: {}",
                "[CMD]".bright_blue(),
                full_command.bright_green());

        println!("{} 在主机 {} 上执行命令: {}",
                "[SSH]".bright_blue(),
                self.name.bright_yellow(),
                command_str.bright_green());

        let output = cmd!("ssh", &connection, &command_str).stdout_capture().stderr_capture().run()?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(eyre!("SSH命令执行失败: {}", stderr));
        }

        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    }

    /// 使用rsync上传文件
    pub fn rsync_upload(&self, local_path: String, remote_path: String) -> Result<()> {
        // 展开本地路径中的波浪号(~)
        let expanded_local_path = Self::expand_tilde(&local_path);
        
        // 构建SSH连接参数
        let ssh_params = if !self.pem.is_empty() {
            if self.use_proxy {
                format!("ssh -i '~/Script/remote/aws/{}' -p {} -o ProxyCommand='ssh -W %h:%p bwg'", self.pem, self.port)
            } else {
                format!("ssh -i '~/Script/remote/aws/{}' -p {}", self.pem, self.port)
            }
        } else {
            if self.use_proxy {
                format!("ssh -p {} -o ProxyCommand='ssh -W %h:%p bwg'", self.port)
            } else {
                format!("ssh -p {}", self.port)
            }
        };

        // 打印完整的格式化后的命令语句
        let full_command = format!("rsync -avz --progress -e \"{}\" {} {}@{}:{}", ssh_params, local_path, self.user, self.ip, remote_path);
        println!("{} 执行命令: {}",
                "[CMD]".bright_blue(),
                full_command.bright_green());

        println!("{} 上传文件到主机 {}: {} -> {}",
                "[RSYNC]".bright_blue(),
                self.name.bright_yellow(),
                local_path.bright_cyan(),
                remote_path.bright_cyan());

        // 使用duct执行rsync命令
        let output = cmd!(
            "rsync",
            "-avz",
            "--progress",
            "-e",
            &ssh_params,
            &expanded_local_path,
            &format!("{}@{}:{}", self.user, self.ip, remote_path)
        ).read();

        match output {
            Ok(out) => {
                println!("{}", out);
                println!("{} 文件上传成功", "[RSYNC]".bright_green());
                Ok(())
            }
            Err(e) => {
                eprintln!("{} rsync命令执行失败: {}", "[ERROR]".bright_red(), e);
                Err(eyre!("rsync命令执行失败: {}", e))
            }
        }
    }

    /// 使用rsync下载文件
    pub fn rsync_download(&self, remote_path: String, local_path: String) -> Result<()> {
        // 展开本地路径中的波浪号(~)
        let expanded_local_path = Self::expand_tilde(&local_path);
        
        // 构建SSH连接参数
        let ssh_params = if !self.pem.is_empty() {
            if self.use_proxy {
                format!("ssh -i '~/Script/remote/aws/{}' -p {} -o ProxyCommand='ssh -W %h:%p bwg'", self.pem, self.port)
            } else {
                format!("ssh -i '~/Script/remote/aws/{}' -p {}", self.pem, self.port)
            }
        } else {
            if self.use_proxy {
                format!("ssh -p {} -o ProxyCommand='ssh -W %h:%p bwg'", self.port)
            } else {
                format!("ssh -p {}", self.port)
            }
        };

        // 打印完整的格式化后的命令语句
        let full_command = format!("rsync -avz --progress -e \"{}\" {}@{}:{} {}", ssh_params, self.user, self.ip, &remote_path, &local_path);
        println!("{} 执行命令: {}",
                "[CMD]".bright_blue(),
                full_command.bright_green());

        println!("{} 从主机 {} 下载文件: {} -> {}",
                "[RSYNC]".bright_blue(),
                self.name.bright_yellow(),
                remote_path.bright_cyan(),
                local_path.bright_cyan());

        // 使用duct执行rsync命令
        let output = cmd!(
            "rsync",
            "-avz",
            "--progress",
            "-e",
            &ssh_params,
            &format!("{}@{}:{}", self.user, self.ip, &remote_path),
            &expanded_local_path
        ).read();

        match output {
            Ok(out) => {
                println!("{}", out);
                println!("{} 文件下载成功", "[RSYNC]".bright_green());
                Ok(())
            }
            Err(e) => {
                eprintln!("{} rsync命令执行失败: {}", "[ERROR]".bright_red(), e);
                Err(eyre!("rsync命令执行失败: {}", e))
            }
        }
    }

    /// PM2相关操作
    pub fn pm2_list(&self) -> Result<String> {
        self.ssh(&["pm2", "list"])
    }

    /// 重启PM2进程
    pub fn pm2_restart(&self, chain: &str, is_slave: bool) -> Result<()> {
        let process_name = if is_slave {
            format!("{}_slave", chain)
        } else {
            chain.to_string()
        };

        println!("{} 重启PM2进程: {} 在主机 {}",
                "[PM2]".bright_blue(),
                process_name.bright_yellow(),
                self.name.bright_yellow());

        self.ssh(&["pm2", "restart", &process_name])?;
        Ok(())
    }

    /// 停止所有PM2进程
    pub fn pm2_stop_all(&self, chain: &str, is_slave: bool) -> Result<()> {
        let process_name = if is_slave {
            format!("{}_slave", chain)
        } else {
            chain.to_string()
        };

        println!("{} 停止PM2进程: {} 在主机 {}",
                "[PM2]".bright_blue(),
                process_name.bright_yellow(),
                self.name.bright_yellow());

        self.ssh(&["pm2", "stop", &process_name])?;
        Ok(())
    }

    /// 上传bot代码
    pub fn upload_bot(&self, chain: &str) -> Result<()> {
        println!("{} 上传bot代码到主机 {} (链: {})",
                "[UPLOAD]".bright_blue(),
                self.name.bright_yellow(),
                chain.to_string().bright_green());

        // 上传Rust二进制文件并命名成CHAIN
        self.rsync_upload(
            self._local_linux_base_path.clone(),
            format!("{}/{}", self._remote_base_path, chain)
        )?;
        Ok(())
    }

    /// 上传路由配置
    pub fn upload_router(&self, chain: &str) -> Result<()> {
        println!("{} 上传路由配置到主机 {} (链: {})",
                "[UPLOAD]".bright_blue(),
                self.name.bright_yellow(),
                chain.to_string().bright_green());

        // 上传路由相关配置文件
        let config_files = [
            "checkpoint.json.gz",
        ];

        for file in &config_files {
            let local_path = Self::expand_tilde(&format!("{}/{}/{}", self._local_data_base_path, chain, file));
            if Path::new(&local_path).exists() {
                self.rsync_upload(
                    local_path,
                    format!("{}/data/{}/{}", self._remote_base_path, chain, file)
                )?;
            } else {
                println!("{} 文件 {} 不存在，跳过上传", "[SKIP]".bright_yellow(), local_path.bright_cyan());
            }
        }

        Ok(())
    }

    /// 上传数据文件
    pub fn upload_data(&self, chain: &str) -> Result<()> {
        println!("{} 上传数据文件到主机 {} (链: {})",
                "[UPLOAD]".bright_blue(),
                self.name.bright_yellow(),
                chain.to_string().bright_green());

        // 上传数据目录
        self.rsync_upload(
            format!("{}/{}/", self._local_data_base_path, chain),
            format!("{}/data/{}", self._remote_base_path, chain))?;
        Ok(())
    }

    /// 下载数据文件
    pub fn download_data(&self, chain: &str) -> Result<()> {
        println!("{} 从主机 {} 下载数据文件 (链: {})",
                "[DOWNLOAD]".bright_blue(),
                self.name.bright_yellow(),
                chain.to_string().bright_green());

        // 下载数据目录
        self.rsync_download(
            format!("{}/data/{}/", self._remote_base_path, chain), 
            format!("./data/{}/", chain)
        )?;

        Ok(())
    }

    /// 下载路由配置
    pub fn download_router(&self, chain: &str) -> Result<()> {
        println!("{} 从主机 {} 下载路由配置 (链: {})",
                "[DOWNLOAD]".bright_blue(),
                self.name.bright_yellow(),
                chain.to_string().bright_green());

        let config_files = [
            "checkpoint.json.gz",
        ];

        for file in &config_files {
            let local_path = format!("{}/{}/{}", self._local_data_base_path, chain, file);
            let remote_path = format!("{}/data/{}/{}", self._remote_base_path, chain, file);
            if let Ok(_) = self.ssh(&["test", "-f", &remote_path]) {
                self.rsync_download(remote_path, local_path)?;
            } else {
                println!("{} 文件 {} 不存在，跳过下载", "[SKIP]".bright_yellow(), remote_path.bright_cyan());
            }
        }

        Ok(())
    }
}

fn server_cfg() -> HashMap<String, Host> {
    let data = vec![
        Host::new("ali_hk", "************", 22, "", "root"),
        Host::new("ali_hk2", "**************", 22, "", "root"),
        Host::new("ali_hk3", "************", 22, "", "root"),
    ];

    //使用name作为key，host作为value新建hashmap
    let mut list = HashMap::new();
    for host in data {
        list.insert(host.name.clone(), host);
    }

    //println!("{:?}", list);
    list
}

fn chain_cfg() -> HashMap<String, Vec<String>> {
    let data = vec![
        ("oec", vec!["ali_hk", "ali_hk2", "ali_hk3"]),
        ("x", vec!["ali_hk"]),
    ];
    let mut list = HashMap::new();
    for (chain, hosts) in data {
        list.insert(chain.to_string(), hosts.iter().map(|host| host.to_string()).collect());
    }
    //println!("{:?}", list);
    list
}

pub fn get_chain_hosts(chain: &str) -> Vec<Host> {
    let chain_cfg = chain_cfg();
    let hosts = chain_cfg.get(chain).expect("chain not found");
    let server_list = server_cfg();
    hosts.iter().map(|host_name| server_list.get(host_name).unwrap().clone()).collect()
}

