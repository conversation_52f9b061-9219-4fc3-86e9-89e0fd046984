use crate::{
    config::{ChainConfig, TokenConfig},
    tools::lowc,
    vira::dex::{
        factory::{DexFactory, FactoryConfig},
        uni_v2::{factory::UniV2Factory, UniV2},
        DexRouter
    }
};


pub fn new() -> ChainConfig {
    let mut c = ChainConfig::new()
        .name("x")
        .server("wss://ws.xlayer.tech")
        //.server("https://xlayerrpc.okx.com")
        //.server("https://rpc.ankr.com/xlayer")
        /* BOT CONTRACT ADDRESS
         * 该合约是整个系统的核心组件,负责处理所有的交易逻辑。
         * 请确保该地址正确无误,否则会导致系统无法正常工作。
         */
        .contract("******************************************") //logic : ******************************************
        /*
         *
         *
         * END
         */

        
        .eth("******************************************")
        .stables(vec![
            "******************************************",
        ])
        .stable_matrix(vec![
            vec!["******************************************"]
        ])
        .tokens(vec![
            TokenConfig::new().name("wokb").addr("******************************************").is_eth().price(200.0).decimals(18),
        ])
        //for decode swap
        .routers(vec![
            UniV2::new(FactoryConfig::new().name("abs").addr("******************************************").fee(30)),
            UniV2::new(FactoryConfig::new().name("revo").addr("******************************************").fee(20)),
            UniV2::new(FactoryConfig::new().name("dyo").addr("******************************************").fee(30)),
            UniV2::new(FactoryConfig::new().name("dyo2").addr("******************************************").fee(30)),
            UniV2::new(FactoryConfig::new().name("dackie").addr("******************************************").fee(25)),
            UniV2::new(FactoryConfig::new().name("potato").addr("******************************************").fee(25)),
            UniV2::new(FactoryConfig::new().name("lfg").addr("******************************************").fee(30)),
        ])
        .factories(vec![
            UniV2Factory::new(FactoryConfig::new().name("abs").addr("0xa7afb6163c331ddb0845843889d6f9544328846f").fee(30)),
            UniV2Factory::new(FactoryConfig::new().name("revo").addr("0xa38498983e7b31de851e36090bc9d1d8fb96be5e").fee(20)),
            UniV2Factory::new(FactoryConfig::new().name("dyo").addr("0x2ccadb1e437aa9cdc741574bda154686b1f04c09").fee(30)),
            UniV2Factory::new(FactoryConfig::new().name("dyo2").addr("0x2ccadb1e437aa9cdc741574bda154686b1f04c09").fee(30)),
            UniV2Factory::new(FactoryConfig::new().name("dackie").addr("0x757cd583004400ee67e5cc3c7a60c6a62e3f6d30").fee(25)),
            UniV2Factory::new(FactoryConfig::new().name("potato").addr("0x630db8e822805c82ca40a54dae02dd5ac31f7fcf").fee(25)),
            UniV2Factory::new(FactoryConfig::new().name("lfg").addr("0x0f6dcc7a1b4665b93f1c639527336540b2076449").fee(30)),
        ])
        .operators(vec![
            vec![
                (lowc("0xbf08035f0784f903292aaa27617ed5f1f282941f"), lowc("04c068b8f82714cd94d726e039551f33723f7122f8682b478cbcdef7da6fde7f")),
                //(lowc("0x89a8b8ae3a2a99ee6d6cb30a518705b5326816b1"), lowc("7619343a0a42476e5a8dff44a83b7283c9ca594442a66e317c4a0095fbbed8b3")),
                ],
            vec![
                //(lowc("0xd8473c2e7b0c6c5c48adb3b72cf7bd79374d734b"), lowc("2b5ef1760ab306659b13b76789cbbdf9c3b21ba416e9982dc387f7473174b462")),
                //(lowc("0x399e380554b106edccc7e1326528cfcf4d7da344"), lowc("5d0bff3e0b4140ca645231a3ace6c16e7e3ea7e388ec227f1aa6dafe04a47fdd")),
            ]
        ])
        .build();
        c.fix_gas = Some(0.10001001);
        c.block_time = 1;
        c.concurrency.max_task = 1;
        c.concurrency.delay_ms_on_low_level_request = 35;
        c.concurrency.batch_sync = 30;
        c.max_gas_feed = 0.15;
        c.feed_freq_sec = 5 * 60; //TODO: 测试5分钟
        c
}


mod tests {
    #[test]
    fn test_config(){
        let config = super::new();
        println!("config: {:?}", config);
    }
}