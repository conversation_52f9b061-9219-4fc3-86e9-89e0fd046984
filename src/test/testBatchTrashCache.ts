import { FindPathResult, MixGas } from "../lib/type/DataType";
import { macro } from "../lib/macro";
import bot from "../bot";
import BotTrash from "../lib/botPumpTrash";

/**
 * 测试 BotTrash 缓存机制
 * 验证缓存功能是否正常工作
 */
class TestBatchTrashCache {
    private botTrash: BotTrash;

    constructor() {
        // 初始化 BotTrash 实例（需要提供钱包和地址）
        const wallets = { "test": "******************************************123456789012345678901234" };
        const addr = "******************************************";
        this.botTrash = new BotTrash(wallets, addr);
    }

    /**
     * 创建测试用的 FindPathResult
     */
    private createTestPath(pairs: string[]): FindPathResult {
        const path = new FindPathResult();
        path.pairs = pairs;
        path.stable = "******************************************";
        path.amount = macro.bn.zero;
        path.reward = 100;
        path.rewardValue = 100;
        return path;
    }

    /**
     * 测试基本缓存功能
     */
    async testBasicCaching() {
        console.log("=== 测试基本缓存功能 ===");
        
        // 创建测试路径
        const path1 = this.createTestPath(["0xaaa", "0xbbb", "0xccc"]);
        const path2 = this.createTestPath(["0xddd", "0xeee", "0xfff"]);
        const path3 = this.createTestPath(["0xaaa", "0xbbb", "0xccc"]); // 与 path1 相同
        
        const mixGas = new MixGas();
        const opIndex = 0;

        console.log("初始缓存状态:", this.botTrash.getCacheStatus());

        // 第一次执行
        console.log("\n第一次执行 - 应该执行所有路径:");
        await this.botTrash.batchTrash([path1, path2], mixGas, opIndex, false, undefined, false);
        
        console.log("执行后缓存状态:", this.botTrash.getCacheStatus());

        // 第二次执行相同路径 - 应该被缓存
        console.log("\n第二次执行相同路径 - 应该被缓存:");
        await this.botTrash.batchTrash([path1, path2], mixGas, opIndex, false, undefined, false);

        // 第三次执行部分相同路径
        console.log("\n第三次执行部分相同路径 - 应该只执行新路径:");
        await this.botTrash.batchTrash([path1, path3], mixGas, opIndex, false, undefined, false);

        console.log("最终缓存状态:", this.botTrash.getCacheStatus());
    }

    /**
     * 测试区块变化时的缓存清理
     */
    async testBlockChangeCacheClear() {
        console.log("\n=== 测试区块变化时的缓存清理 ===");
        
        const path1 = this.createTestPath(["0x111", "0x222"]);
        const mixGas = new MixGas();
        const opIndex = 0;

        // 执行一次以建立缓存
        console.log("建立缓存:");
        await this.botTrash.batchTrash([path1], mixGas, opIndex, false, undefined, false);
        console.log("缓存状态:", this.botTrash.getCacheStatus());

        // 模拟区块号变化（这里需要手动触发，因为在测试环境中区块号可能不会自动变化）
        console.log("\n模拟区块号变化...");
        
        // 再次执行相同路径 - 由于区块号变化，应该重新执行
        console.log("区块变化后执行相同路径 - 应该重新执行:");
        await this.botTrash.batchTrash([path1], mixGas, opIndex, false, undefined, false);
        console.log("执行后缓存状态:", this.botTrash.getCacheStatus());
    }

    /**
     * 测试手动清理缓存
     */
    async testManualCacheClear() {
        console.log("\n=== 测试手动清理缓存 ===");
        
        const path1 = this.createTestPath(["0xabc", "0xdef"]);
        const mixGas = new MixGas();
        const opIndex = 0;

        // 建立缓存
        await this.botTrash.batchTrash([path1], mixGas, opIndex, false, undefined, false);
        console.log("建立缓存后状态:", this.botTrash.getCacheStatus());

        // 手动清理缓存
        this.botTrash.clearCache();
        console.log("手动清理后状态:", this.botTrash.getCacheStatus());

        // 再次执行 - 应该重新执行
        console.log("清理后执行相同路径 - 应该重新执行:");
        await this.botTrash.batchTrash([path1], mixGas, opIndex, false, undefined, false);
        console.log("最终状态:", this.botTrash.getCacheStatus());
    }

    /**
     * 测试特征值生成的唯一性
     */
    testSignatureUniqueness() {
        console.log("\n=== 测试特征值生成的唯一性 ===");
        
        const path1 = this.createTestPath(["0x111", "0x222", "0x333"]);
        const path2 = this.createTestPath(["0x111", "0x222", "0x444"]); // 不同的最后一个pair
        const path3 = this.createTestPath(["0x222", "0x111", "0x333"]); // 不同的顺序
        const path4 = this.createTestPath(["0x111", "0x222", "0x333"]); // 与path1相同

        // 由于 generatePathSignature 是私有方法，我们通过执行来间接测试
        console.log("测试不同路径应该生成不同的特征值...");
        console.log("Path1 pairs:", path1.pairs);
        console.log("Path2 pairs:", path2.pairs);
        console.log("Path3 pairs:", path3.pairs);
        console.log("Path4 pairs:", path4.pairs);
        console.log("Path1 和 Path4 应该生成相同的特征值");
        console.log("其他路径应该生成不同的特征值");
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        try {
            console.log("开始测试 BotTrash 缓存机制...\n");
            
            await this.testBasicCaching();
            await this.testBlockChangeCacheClear();
            await this.testManualCacheClear();
            this.testSignatureUniqueness();
            
            console.log("\n=== 所有测试完成 ===");
            console.log("注意：由于测试环境限制，某些功能（如实际的区块号变化）可能需要在真实环境中验证。");
            
        } catch (error) {
            console.error("测试过程中发生错误:", error);
        }
    }
}

// 导出测试类
export default TestBatchTrashCache;

// 如果直接运行此文件，执行测试
if (require.main === module) {
    const test = new TestBatchTrashCache();
    test.runAllTests();
}
