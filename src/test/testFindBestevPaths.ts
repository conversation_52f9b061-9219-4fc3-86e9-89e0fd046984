import { <PERSON><PERSON><PERSON>ber } from "ethers";
import bot from "../bot";
import BotJunk from "../lib/botPumpJunk";
import { MevPath } from "../lib/type/DataType";
import { PairExtend } from "../lib/type/Pair";
import { TokenExtend } from "../lib/type/Token";
import { macro } from "../lib/macro";

// Mock bot and its dependencies for testing
const mockBot: any = {
    oracleV2: {
        data: {
            pm: {
                data: new Map<string, PairExtend>()
            }
        }
    },
    token: jest.fn()
};

// Mock the bot import
jest.mock("../bot", () => mockBot);

describe("BotJunk.findBestevPaths", () => {
    let botJunk: BotJunk;
    
    beforeEach(() => {
        // Create a BotJunk instance with mock data
        botJunk = new (BotJunk as any)({}, "");
        
        // Clear mock data
        mockBot.oracleV2.data.pm.data.clear();
        mockBot.token.mockReset();
    });

    it("should return empty array when no pairs exist", () => {
        const result = botJunk.findBestMevPaths();
        expect(result).toEqual([]);
    });

    it("should return empty array when pairs have no mev paths", () => {
        // Create a pair with no mev paths
        const pair = new PairExtend();
        pair.address = "0x123";
        mockBot.oracleV2.data.pm.data.set("0x123", pair);
        
        const result = botJunk.findBestMevPaths();
        expect(result).toEqual([]);
    });

    it("should return the best 20 mev paths sorted by value", () => {
        // Create mock tokens with prices
        const tokenA: any = { price: 2 }; // $2
        const tokenB: any = { price: 1 }; // $1
        const tokenC: any = { price: 3 }; // $3
        
        mockBot.token.mockImplementation((addr: string) => {
            if (addr === "0xA") return tokenA;
            if (addr === "0xB") return tokenB;
            if (addr === "0xC") return tokenC;
            return { price: 0 };
        });
        
        // Create pairs with mev paths
        const pair1 = new PairExtend();
        pair1.address = "0x1";
        pair1.mevPath = [
            { s0: "0xA", weight: 10 } as MevPath, // Value: 2 * 10 = 20
            { s0: "0xB", weight: 15 } as MevPath  // Value: 1 * 15 = 15
        ];
        
        const pair2 = new PairExtend();
        pair2.address = "0x2";
        pair2.mevPath = [
            { s0: "0xC", weight: 5 } as MevPath,  // Value: 3 * 5 = 15
            { s0: "0xA", weight: 7 } as MevPath   // Value: 2 * 7 = 14
        ];
        
        const pair3 = new PairExtend();
        pair3.address = "0x3";
        pair3.mevPath = [
            { s0: "0xB", weight: 25 } as MevPath  // Value: 1 * 25 = 25
        ];
        
        mockBot.oracleV2.data.pm.data.set("0x1", pair1);
        mockBot.oracleV2.data.pm.data.set("0x2", pair2);
        mockBot.oracleV2.data.pm.data.set("0x3", pair3);
        
        const result = botJunk.findBestMevPaths();
        
        // Should return 5 paths (less than 20, so all)
        expect(result).toHaveLength(5);
        
        // Check if they are sorted by value (descending)
        // Expected order by value: 25, 20, 15, 15, 14
        // But we're only checking the first few for simplicity
        expect(result[0]).toEqual(pair3.mevPath[0]); // Value: 25
        expect(result[1]).toEqual(pair1.mevPath[0]); // Value: 20
    });

    it("should return maximum 20 paths even when more exist", () => {
        // Create mock token
        const token: any = { price: 1 };
        mockBot.token.mockReturnValue(token);
        
        // Create a pair with 25 mev paths
        const pair = new PairExtend();
        pair.address = "0x1";
        pair.mevPath = Array(25).fill(0).map((_, i) => ({
            s0: "0xA",
            weight: i + 1 // Values from 1 to 25
        } as MevPath));
        
        mockBot.oracleV2.data.pm.data.set("0x1", pair);
        
        const result = botJunk.findBestMevPaths();
        
        // Should return exactly 20 paths
        expect(result).toHaveLength(20);
        
        // Should be sorted by value (descending)
        for (let i = 0; i < result.length - 1; i++) {
            // Since all have the same token price, sorting by weight is enough
            expect((result[i] as any).weight).toBeGreaterThanOrEqual((result[i + 1] as any).weight);
        }
        
        // First should have weight 25, last should have weight 6
        expect(result[0].weight).toBe(25);
        expect(result[19].weight).toBe(6);
    });
});