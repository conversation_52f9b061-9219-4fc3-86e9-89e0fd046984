#[derive(<PERSON>bu<PERSON>, De<PERSON>ult)]
pub struct Profit {
    pub profit : f32,
    pub cost : f32,
    pub expect : f32,
}

#[derive(Debu<PERSON>, De<PERSON>ult)]
pub struct Profits {
    trash : Profit,
    pump : Profit,
    junk : Profit,
}


pub struct Record {
    pub profit : Profits,
}


impl Record {
    pub fn record_trash(&mut self, profit:f32, cost:f32, expect:f32){
        self.profit.trash.profit += profit;
        self.profit.trash.cost += cost;
        self.profit.trash.expect += expect;
    }
}