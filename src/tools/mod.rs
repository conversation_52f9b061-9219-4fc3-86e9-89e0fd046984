use alloy::primitives::U256;

pub mod record;

/// 将给定的字符串转换为小写。
///
/// 这个函数接受一个字符串引用，并返回一个新的字符串，
/// 其中所有的字符都被转换为小写。
///
/// # Examples
///
/// ```
/// let s = "HELLO WORLD";
/// let lowercased = lowc(s);
/// assert_eq!(lowercased, "hello world");
/// ```
pub fn lowc(str : &str) -> String {
    str.to_lowercase().to_string()
}

pub fn _str(s: &str) -> String {
    s.to_string()
}

//等值计算
pub fn _equal_ratio(prev_value: U256, prev_amount: U256, r_in: U256) -> U256 {
    if prev_value == U256::ZERO {
        return r_in;
    }
    prev_value * r_in / prev_amount
}

//打印当前时间, 格式为: 2024-03-01 12:00:00.000
pub fn now_str() -> String {
    let now = chrono::Local::now();
    now.format("%H:%M:%S.%3f").to_string()
}

pub fn _now_ts_sec() -> u64 {
    //获取当前时间戳的秒数
    chrono::Utc::now().timestamp() as u64
}

pub fn sqrt(input: U256) -> U256 {
    if input == U256::ZERO {
        return U256::ZERO;
    }
 
    let mut z = (input + U256::from(1)) / U256::from(2);
    let mut y = input;
    while z < y {
        y = z;
        z = (input / z + z) / U256::from(2);
    }
    y
}


#[cfg(test)]
mod test {
    #[tokio::test]
    async fn test_sort_by() {
        let mut weights = vec![(524053.66f32, 4), (600.0f32,4), (424053.66f32, 3)];
        weights.sort_by(|a, b| {
            //b.0.partial_cmp(&a.0).unwrap()
            (b.0 * 0.6f32.powi(b.1)).partial_cmp(&(a.0 * 0.6f32.powi(a.1))).unwrap()
        });
        dbg!(weights);
    }
}