# 服务器管理器 (Server Manager)

这个Rust实现的服务器管理器提供了与原TypeScript版本`server.ts`相同的功能，用于管理多个远程服务器上的MEV bot部署。

## 功能特性

### 🚀 核心功能
- **多服务器管理**: 支持管理AWS、阿里云、Google Cloud、Azure等多个云服务商的服务器
- **自动化部署**: 自动上传bot代码、配置文件和数据到远程服务器
- **进程管理**: 使用PM2管理远程服务器上的bot进程
- **链配置管理**: 为不同的区块链配置对应的服务器集群
- **批量操作**: 支持批量更新所有从服务器

### 🔧 技术实现
- **异步操作**: 使用Tokio实现高性能异步操作
- **错误处理**: 完善的错误处理和日志记录
- **类型安全**: 使用Rust的类型系统确保配置正确性
- **彩色输出**: 友好的命令行界面和状态显示

## 使用方法

### 基本命令

```bash
# 构建项目
cargo build --release

# 列出所有Google Cloud主机的PM2状态
cargo run --bin server_manager ls

# 从主服务器下载数据
cargo run --bin server_manager OEC d

# 从主服务器下载路由配置
cargo run --bin server_manager OEC d_router

# 上传bot到主服务器
cargo run --bin server_manager OEC u_bot

# 上传数据到主服务器（谨慎使用）
cargo run --bin server_manager OEC u_data

# 上传路由配置到主服务器
cargo run --bin server_manager OEC u_router
```

### 批量操作

```bash
# 上传bot到所有从服务器并重启
cargo run --bin server_manager OEC u_all_bot

# 上传路由配置到所有从服务器并重启
cargo run --bin server_manager OEC u_all_router

# 上传所有文件到从服务器并重启（完整部署）
cargo run --bin server_manager OEC u_all

# 停止所有服务器上的进程
cargo run --bin server_manager OEC stop_all

# 测试操作（重启主服务器上的进程）
cargo run --bin server_manager OEC test
```

## 支持的区块链

| 链名称 | 代码 | 配置的服务器 |
|--------|------|-------------|
| OKExChain | OEC | gc_sg |
| REI Network | REI | ali_hk |
| KardiaChain | KAI | gc_sg |
| Bitkub Chain | KUB | ali_sg |
| Huobi ECO Chain | HECO | aws_jp |
| KuCoin Community Chain | KCC | aws_jp |
| Astar Network | ASTR | ali_de |
| Klaytn | KLAY | aws_kr |
| Moonbeam | GLMR | gc_nl, gc_vg, gc_fr |
| Moonriver | MOVR | ali_de, gc_nl, gc_fr |
| Gnosis Chain | XDAI | ali_de, gc_nl, gc_fr, aws_de |
| Polygon | POLYGON | ali_de |
| Celo | CELO | ali_de |
| EthereumPoW | ETHW | ali_de, gc_la |
| BNB Smart Chain | BSC | ali_de |
| Harmony | ONE | ali_de, gc_nl |
| Arbitrum | ARB | ali_de |
| Metis | METIS | ali_de, aws_de |
| CORE | CORE | aws_vg |
| PulseChain | PLS | hz_hel |
| Kava | KAVA | aws_vg |
| Base | BASE | aws_vg |
| opBNB | OPBNB | aws_vg |
| WEMIX | WEMIX | aws_jp |
| Elastos | ELA | home, gc_vg |
| ZetaChain | ZETA | aws_vg |
| Optimism | OP | gc_vg |
| MXC | MXC | gc_la, gc_vg |
| Degen Chain | DEGEN | gc_la |
| BEVM | BEVM | gc_vg |
| Sei Network | SEI | gc_sg |

## 服务器配置

### 主机类型
- **ali_**: 阿里云服务器
- **aws_**: Amazon Web Services
- **gc_**: Google Cloud Platform  
- **az_**: Microsoft Azure
- **hz_**: Hetzner
- **bwg**: BandwagonHost
- **home**: 本地服务器

### 认证方式
- **PEM密钥**: AWS和GCP服务器使用PEM密钥文件认证
- **密码认证**: 阿里云等服务器使用用户名密码认证
- **代理连接**: 部分服务器通过跳板机连接

## 架构设计

### 核心组件

1. **Chain枚举**: 定义所有支持的区块链类型
2. **Host结构体**: 封装单个服务器的连接信息和操作方法
3. **Server结构体**: 管理所有主机和链配置的映射关系
4. **ServerArgs**: 处理命令行参数解析

### 操作流程

1. **初始化**: 加载所有主机配置和链映射关系
2. **参数解析**: 解析命令行参数确定目标链和操作
3. **主机选择**: 根据链配置选择对应的主机列表
4. **操作执行**: 在选定的主机上执行相应操作
5. **状态反馈**: 提供详细的操作状态和结果反馈

## 安全注意事项

⚠️ **重要提醒**:
- `u_data`操作会覆盖远程服务器上的数据，使用前请确认
- 确保SSH密钥文件权限正确设置（600）
- 生产环境操作前建议先在测试环境验证
- 批量操作会影响多个服务器，请谨慎使用

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查网络连接和防火墙设置
   - 验证SSH密钥文件路径和权限
   - 确认服务器IP和端口正确

2. **文件传输失败**
   - 检查本地文件是否存在
   - 验证远程目录权限
   - 确认磁盘空间充足

3. **PM2操作失败**
   - 确认PM2已在远程服务器安装
   - 检查进程名称是否正确
   - 验证Node.js环境配置

### 日志查看

程序会输出详细的操作日志，包括：
- 连接状态
- 文件传输进度
- 命令执行结果
- 错误信息和堆栈

## 开发和扩展

### 添加新的区块链支持

1. 在`Chain`枚举中添加新的链类型
2. 在`init_chain_config`方法中配置对应的服务器
3. 更新文档和测试用例

### 添加新的服务器

1. 在`init_hosts`方法中添加主机配置
2. 在相应的链配置中引用新主机
3. 测试连接和操作功能

### 自定义操作

可以在`execute_action`方法中添加新的操作类型，实现特定的部署和管理需求。

## 许可证

本项目遵循与主项目相同的许可证。
