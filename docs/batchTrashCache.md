# BotTrash 缓存机制文档

## 概述

为 `batchTrash` 函数实现了一个高效的缓存机制，用于避免在同一个区块内重复执行相同的交易路径，从而提高系统效率并避免不必要的重复交易。

## 设计特点

### 1. 缓存机制设计

- **缓存当前区块号**: 使用 `bot.handle.blockNum` 获取当前区块号
- **路径特征值生成**: 为每个 `FindPathResult` 生成唯一的特征值
- **特征值组成**: `${blockNum}_${pairs.join(',')}`
  - 当前区块号（blocknum）
  - FindPathResult.pairs 数组的内容（用逗号连接）

### 2. 缓存逻辑

1. **路径过滤**: 每次调用 `batchTrash` 函数时，首先生成当前 paths 的所有特征值
2. **缓存检查**: 检查缓存中是否已存在相同的特征值，过滤掉已存在特征值的 path
3. **执行控制**: 如果剩余的 path 数量为 0，则跳过后续逻辑

### 3. 缓存清理

- **自动清理**: 当检测到区块号发生变化时，自动清空所有缓存的特征值
- **作用域**: 确保缓存只在同一个区块内有效

## 实现细节

### 核心属性

```typescript
// 缓存机制相关属性
private pathSignatureCache = new Set<string>(); // 存储已执行的路径特征值
private cachedBlockNum = -1; // 缓存的区块号
```

### 核心方法

#### 1. 生成路径特征值

```typescript
private generatePathSignature(path: FindPathResult): string {
    const blockNum = bot.handle.blockNum;
    const pairsStr = path.pairs.join(',');
    return `${blockNum}_${pairsStr}`;
}
```

#### 2. 更新缓存

```typescript
private updateCache(): void {
    const currentBlockNum = bot.handle.blockNum;
    
    // 如果区块号发生变化，清空所有缓存
    if (this.cachedBlockNum !== currentBlockNum) {
        this.pathSignatureCache.clear();
        this.cachedBlockNum = currentBlockNum;
    }
}
```

#### 3. 过滤缓存路径

```typescript
private filterCachedPaths(paths: FindPathResult[]): FindPathResult[] {
    this.updateCache();
    
    const filteredPaths: FindPathResult[] = [];
    const newSignatures: string[] = [];
    
    for (const path of paths) {
        const signature = this.generatePathSignature(path);
        
        // 如果缓存中不存在该特征值，则添加到结果中
        if (!this.pathSignatureCache.has(signature)) {
            filteredPaths.push(path);
            newSignatures.push(signature);
        }
    }
    
    // 将新的特征值添加到缓存中
    newSignatures.forEach(sig => this.pathSignatureCache.add(sig));
    
    return filteredPaths;
}
```

### 集成到 batchTrash

```typescript
async batchTrash(paths:FindPathResult[], mixGas:MixGas, opIndex:number, bid=false, customNonce?:number, estimateReward=true){
    // 应用缓存机制，过滤掉已执行的路径
    const filteredPaths = this.filterCachedPaths(paths);
    
    // 如果所有路径都被缓存过，直接返回
    if (filteredPaths.length === 0) {
        Lazy.ins().logTs(`${macro.COLOR.BYellow}[trash] All paths cached, skipping execution${macro.COLOR.Off}`);
        return;
    }
    
    // 使用过滤后的路径继续执行
    let s = DataType.findPathResultsToServerPumpData(filteredPaths);
    // ... 其余逻辑
}
```

## 性能特点

### 1. 高性能特征值生成

- **字符串拼接**: 使用简单的字符串拼接生成特征值，性能极致快速
- **唯一性保证**: 通过区块号和 pairs 数组内容确保唯一性
- **内存效率**: 使用 `Set<string>` 存储特征值，查找和插入都是 O(1) 时间复杂度

### 2. 自动内存管理

- **区块级缓存**: 缓存只在单个区块内有效，避免内存泄漏
- **自动清理**: 区块变化时自动清理，无需手动管理

## 使用示例

### 基本使用

```typescript
// 创建 BotTrash 实例
const botTrash = new BotTrash(wallets, address);

// 正常调用 batchTrash，缓存机制会自动生效
await botTrash.batchTrash(paths, mixGas, opIndex);

// 相同路径在同一区块内再次调用会被缓存过滤
await botTrash.batchTrash(paths, mixGas, opIndex); // 这次会被跳过
```

### 缓存状态查询

```typescript
// 获取当前缓存状态
const status = botTrash.getCacheStatus();
console.log(`缓存大小: ${status.cacheSize}`);
console.log(`当前区块: ${status.currentBlock}`);
console.log(`缓存区块: ${status.cachedBlock}`);
```

### 手动清理缓存

```typescript
// 在特殊情况下手动清理缓存
botTrash.clearCache();
```

## 日志输出

缓存机制会产生以下日志：

1. **区块变化**: `[Cache] Block changed to {blockNum}, cache cleared`
2. **路径过滤**: `[Cache] Filtered {filtered} cached paths, {remaining} remaining`
3. **全部缓存**: `[trash] All paths cached, skipping execution`
4. **手动清理**: `[Cache] Manually cleared cache`

## 测试

提供了完整的测试套件 `src/test/testBatchTrashCache.ts`，包括：

1. 基本缓存功能测试
2. 区块变化时缓存清理测试
3. 手动清理缓存测试
4. 特征值唯一性测试

运行测试：

```bash
npm run test:cache
```

## 注意事项

1. **区块同步**: 确保 `bot.handle.blockNum` 正确反映当前区块号
2. **内存使用**: 在高频交易场景下，监控缓存大小以确保内存使用合理
3. **测试环境**: 在测试环境中，区块号可能不会自动变化，需要手动模拟
4. **线程安全**: 当前实现假设单线程环境，如需多线程支持需要添加锁机制

## 性能收益

- **减少重复交易**: 避免在同一区块内执行相同路径的重复交易
- **提高响应速度**: 快速过滤已执行路径，减少不必要的计算
- **降低 Gas 消耗**: 避免重复交易导致的 Gas 浪费
- **提升系统稳定性**: 减少网络负载和交易冲突
