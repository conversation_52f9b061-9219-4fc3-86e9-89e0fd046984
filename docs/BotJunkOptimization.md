# BotJunk 利润优化系统

## 概述

在 `src/lib/botPumpJunk.ts` 中实现了一个基于交易对利润统计的垃圾交易优化系统，通过记录和分析历史交易利润来优化交易策略。

## 核心功能

### 1. 利润统计缓存系统

- **缓存结构**: 使用 `Map<string, ProfitCacheItem>` 记录每种交易对组合的利润统计
- **特征值**: 使用标准化的交易对组合作为唯一标识符
- **数据内容**: 累计利润总量、交易次数、最后更新时间
- **缓存时长**: 1小时自动过期

```typescript
interface ProfitCacheItem {
    feature: string;           // 特征值（标准化的交易对组合）
    totalProfit: number;       // 累计利润总量
    lastUpdateTime: number;    // 最后更新时间戳
    count: number;             // 交易次数
}
```

### 2. 实时排行榜

- **排序规则**: 按累计利润总量从大到小排序
- **实时更新**: 每次利润更新后自动重新排序
- **数据结构**: 包含特征值、累计利润、交易次数、平均利润

```typescript
interface RankingItem {
    feature: string;           // 特征值
    totalProfit: number;       // 累计利润总量
    count: number;             // 交易次数
    avgProfit: number;         // 平均利润
}
```

### 3. 特征值标准化

- **目的**: 确保正向和反向交易使用相同的特征值
- **实现**: 比较正向和反向路径的字典序，选择较小的作为标准
- **示例**: `A->B->C` 和 `C->B->A` 会被识别为同一种交易对组合

```typescript
private normalizePathKey(pairs: string[]): string {
    const forwardKey = pairs.join("->");
    const reverseKey = [...pairs].reverse().join("->");
    return forwardKey < reverseKey ? forwardKey : reverseKey;
}
```

## 主要方法

### updateTopMev(f: FindPathResult): void

更新交易对的利润统计信息。

**参数**:
- `f`: FindPathResult 对象，包含交易路径和利润信息

**功能**:
- 提取交易对特征值
- 更新累计利润和交易次数
- 重新构建排行榜
- 输出调试信息

### doJunkNew(): FindPathResult[]

基于利润排行榜的优化垃圾交易函数。

**逻辑**:
1. 检查排行榜是否有数据
2. 获取前10名高利润交易对
3. 从 bestMevs 中找到匹配的路径
4. 生成正向和反向的 FindPathResult
5. 执行 batchTrash 操作

**回退机制**:
- 排行榜为空时回退到原始 doJunk 方法
- 没有匹配路径时回退到原始 doJunk 方法

### 辅助方法

- `getProfitRanking(limit: number)`: 获取指定数量的排行榜数据
- `getProfitCacheStatus()`: 获取缓存系统状态信息
- `displayProfitRanking(limit: number)`: 打印排行榜到控制台
- `cleanExpiredCache()`: 清理过期的缓存记录

## 自动化机制

### 1. 智能切换

在 `init()` 方法中实现了智能切换逻辑：

```typescript
setInterval(() => {
    // 检查是否启用了利润优化系统
    // 如果排行榜有数据且缓存项超过5个，使用优化版本
    if (this.ranking.length > 0 && this.profitCache.size >= 5) {
        this.doJunkNew();
    } else {
        this.doJunk();
    }
}, bot.config.junk.delay_ms);
```

### 2. 定期清理

- **清理频率**: 每10分钟执行一次
- **清理条件**: 超过1小时的缓存记录
- **清理后**: 自动重新构建排行榜

### 3. 状态显示

- **显示频率**: 每5分钟显示一次
- **显示内容**: 前5名排行榜和缓存状态
- **显示条件**: 排行榜有数据时才显示

### 4. 利润统计集成

在 `logTrash()` 方法中集成了利润统计更新：

- 监听交易成功事件
- 提取利润信息
- 构建 FindPathResult 对象
- 调用 updateTopMev 更新统计

## 配置要求

系统依赖现有的 `bot.config.junk` 配置：

```typescript
junk: { 
    active: boolean,      // 是否启用垃圾交易
    delay_ms: number,     // 执行间隔（毫秒）
    pair_num: number      // 交易对数量
}
```

## 性能优化

### 1. 内存管理

- 自动清理过期缓存，防止内存泄漏
- 使用 Map 数据结构提高查找效率
- 排行榜按需重建，避免频繁排序

### 2. 计算优化

- 特征值标准化只在需要时计算
- 排行榜更新采用增量方式
- 缓存状态查询使用缓存结果

### 3. 错误处理

- 利润统计更新包含 try-catch 错误处理
- 回退机制确保系统稳定性
- 详细的调试日志便于问题排查

## 使用示例

```typescript
// 手动更新利润统计
const findPathResult = new FindPathResult();
findPathResult.pairs = ["0xAAA", "0xBBB"];
findPathResult.rewardValue = 150.5;
botJunk.updateTopMev(findPathResult);

// 获取排行榜
const top10 = botJunk.getProfitRanking(10);
console.log("前10名交易对:", top10);

// 显示排行榜
botJunk.displayProfitRanking(5);

// 获取缓存状态
const status = botJunk.getProfitCacheStatus();
console.log("缓存状态:", status);
```

## 监控和调试

系统提供了丰富的日志输出：

- 利润统计更新日志
- 排行榜切换日志
- 缓存清理日志
- 错误处理日志

所有日志都使用颜色标记，便于在控制台中识别。

## 注意事项

1. **数据一致性**: 正向和反向交易会被合并统计
2. **内存使用**: 缓存会占用一定内存，但有自动清理机制
3. **性能影响**: 排行榜重建有一定开销，但频率可控
4. **配置依赖**: 需要确保 bot.config.junk 配置正确
5. **错误恢复**: 系统具有良好的错误恢复能力，不会影响主要交易逻辑
