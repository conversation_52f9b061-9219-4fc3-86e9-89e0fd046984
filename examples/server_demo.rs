use std::collections::HashMap;
use std::path::Path;
use serde::{Deserialize, Serialize};
use tokio::process::Command as AsyncCommand;
use eyre::{Result, eyre};
use colored::*;

/// 支持的区块链类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Chain {
    OEC,
    BSC,
    POLYGON,
    ARB,
    BASE,
}

impl std::fmt::Display for Chain {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}", self)
    }
}

impl std::str::FromStr for Chain {
    type Err = eyre::Error;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_uppercase().as_str() {
            "OEC" => Ok(Chain::OEC),
            "BSC" => Ok(Chain::BSC),
            "POLYGON" => Ok(Chain::POLYGON),
            "ARB" => Ok(Chain::ARB),
            "BASE" => Ok(Chain::BASE),
            _ => Err(eyre!("未知的区块链类型: {}", s)),
        }
    }
}

/// 远程主机配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Host {
    pub name: String,
    pub ip: String,
    pub port: u16,
    pub pem: String,
    pub user: String,
    pub use_proxy: bool,
}

impl Host {
    pub fn new(name: &str, ip: &str, port: u16, pem: &str, user: &str) -> Self {
        Self {
            name: name.to_string(),
            ip: ip.to_string(),
            port,
            pem: pem.to_string(),
            user: user.to_string(),
            use_proxy: false,
        }
    }

    pub fn with_proxy(mut self, use_proxy: bool) -> Self {
        self.use_proxy = use_proxy;
        self
    }

    fn build_ssh_connection(&self) -> String {
        let pem_option = if !self.pem.is_empty() {
            format!(" -i '~/Script/remote/aws/{}'", self.pem)
        } else {
            String::new()
        };

        let proxy_option = if self.use_proxy {
            " -o ProxyCommand='ssh -W %h:%p bwg'"
        } else {
            ""
        };

        format!("{}@{} -p {}{}{}", 
                self.user, self.ip, self.port, pem_option, proxy_option)
    }

    pub async fn ssh(&self, commands: &[&str]) -> Result<String> {
        let connection = self.build_ssh_connection();
        let command_str = commands.join(" && ");
        
        println!("{} 在主机 {} 上执行命令: {}", 
                "[SSH]".bright_blue(), 
                self.name.bright_yellow(), 
                command_str.bright_green());

        // 在演示中，我们只是模拟SSH命令执行
        println!("{} 模拟执行: ssh {} '{}'", 
                "[DEMO]".bright_cyan(),
                connection,
                command_str);

        Ok("命令执行成功".to_string())
    }

    pub async fn pm2_restart(&self, chain: &Chain, is_slave: bool) -> Result<()> {
        let process_name = if is_slave {
            format!("{}_slave", chain)
        } else {
            chain.to_string()
        };

        println!("{} 重启PM2进程: {} 在主机 {}", 
                "[PM2]".bright_blue(), 
                process_name.bright_yellow(), 
                self.name.bright_yellow());

        self.ssh(&["pm2", "restart", &process_name]).await?;
        Ok(())
    }
}

/// 服务器管理器演示
#[derive(Debug)]
pub struct ServerDemo {
    hosts: HashMap<String, Host>,
    chain_config: HashMap<Chain, Vec<String>>,
}

impl ServerDemo {
    pub fn new() -> Self {
        let mut server = Self {
            hosts: HashMap::new(),
            chain_config: HashMap::new(),
        };
        
        server.init_demo_hosts();
        server.init_demo_chain_config();
        
        server
    }

    fn init_demo_hosts(&mut self) {
        // 演示用的主机配置
        self.hosts.insert("demo_server1".to_string(), 
            Host::new("demo_server1", "*************", 22, "", "root"));
        self.hosts.insert("demo_server2".to_string(), 
            Host::new("demo_server2", "*************", 22, "demo.pem", "admin"));
        self.hosts.insert("demo_server3".to_string(), 
            Host::new("demo_server3", "*************", 22, "", "ubuntu").with_proxy(true));
    }

    fn init_demo_chain_config(&mut self) {
        self.chain_config.insert(Chain::OEC, vec!["demo_server1".to_string()]);
        self.chain_config.insert(Chain::BSC, vec!["demo_server1".to_string(), "demo_server2".to_string()]);
        self.chain_config.insert(Chain::POLYGON, vec!["demo_server2".to_string()]);
        self.chain_config.insert(Chain::ARB, vec!["demo_server2".to_string(), "demo_server3".to_string()]);
        self.chain_config.insert(Chain::BASE, vec!["demo_server3".to_string()]);
    }

    pub fn get_hosts_for_chain(&self, chain: &Chain) -> Result<Vec<&Host>> {
        let host_names = self.chain_config.get(chain)
            .ok_or_else(|| eyre!("未找到链 {} 的配置", chain))?;

        let mut hosts = Vec::new();
        for name in host_names {
            let host = self.hosts.get(name)
                .ok_or_else(|| eyre!("未找到主机配置: {}", name))?;
            hosts.push(host);
        }

        Ok(hosts)
    }

    pub async fn demo_operation(&self, chain: &Chain, action: &str) -> Result<()> {
        let hosts = self.get_hosts_for_chain(chain)?;

        println!("{} 演示操作: {} 在链: {}", 
                "[DEMO]".bright_blue(), 
                action.bright_yellow(), 
                chain.to_string().bright_green());

        match action {
            "test" => {
                println!("{} 执行测试操作", "[TEST]".bright_cyan());
                for host in &hosts {
                    host.pm2_restart(chain, false).await?;
                }
            },
            "list_hosts" => {
                println!("{} 列出所有主机:", "[LIST]".bright_cyan());
                for host in &hosts {
                    println!("  - {} ({}:{})", host.name.bright_green(), host.ip, host.port);
                }
            },
            _ => {
                println!("{} 未知操作: {}", "[ERROR]".bright_red(), action);
            }
        }

        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("{} 服务器管理器演示程序", "[DEMO]".bright_blue());
    println!("{}", "=".repeat(50).bright_blue());

    let server = ServerDemo::new();

    // 演示不同链的操作
    let chains = vec![Chain::OEC, Chain::BSC, Chain::POLYGON, Chain::ARB, Chain::BASE];

    for chain in chains {
        println!("\n{} 演示链: {}", "[CHAIN]".bright_green(), chain.to_string().bright_yellow());
        
        // 列出主机
        server.demo_operation(&chain, "list_hosts").await?;
        
        // 执行测试操作
        server.demo_operation(&chain, "test").await?;
        
        println!("{}", "-".repeat(30).bright_black());
    }

    println!("\n{} 演示完成!", "[SUCCESS]".bright_green());
    Ok(())
}
