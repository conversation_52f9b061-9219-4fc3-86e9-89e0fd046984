# 项目结构概览

本项目是一个基于 TypeScript 的机器人，主要用于与区块链环境进行交互，功能可能涵盖自动化交易、数据监控等。

## 关键目录和文件

-   **`/` (根目录)**: 包含项目配置和元数据。
    -   `package.json`: 定义项目元数据、依赖项和脚本。这是理解项目结构的主要入口点。
    -   `tsconfig.json`: TypeScript 配置文件，定义了 TypeScript 代码应如何编译。
    -   `.gitignore`: 指定应被 Git 忽略的文件和目录。
-   **`/src`**: 项目的主要源代码目录。
    -   `main.ts`: 应用程序的主要入口文件。
    -   `bot.ts`: 包含机器人的核心业务逻辑。
    -   `/src/lib`: 包含机器人的各种库文件和功能模块。
        -   `/src/lib/chain`: 包含与特定区块链交互相关的代码。
        -   `/src/lib/comp`: 包含机器人的各种可复用组件。
        -   `/src/lib/config`: 包含不同链或环境的配置文件。
        -   `/src/lib/contract`: 包含与智能合约交互的逻辑。
        -   `/src/lib/provider`: 包含与区块链节点提供者（Provider）相关的逻辑。
-   **`/src/sol`**: 包含 Solidity 智能合约。这些合约可能会被部署到链上，并由机器人进行交互。
-   **`/wallets`**: 包含钱包信息，用于不同的区块链网络。

## 如何理解代码

1.  **从 `package.json` 开始**：此文件可以让你对项目的依赖和脚本有一个总体了解。
2.  **查看 `src/main.ts`**：这是应用程序的入口，它会告诉你机器人的不同部分是如何初始化和协同工作的。
3.  **探索 `/src/lib`**：此目录包含机器人的核心逻辑。你可以探索其子目录来理解机器人的不同功能。
4.  **检查 `/src/sol`**：此目录包含机器人与之交互的 Solidity 智能合约。

## 常用 `glob` 表达式

-   列出所有文件: `glob("**")`
-   列出所有TypeScript源文件: `glob("src/**/*.ts")`
-   列出所有Solidity合约文件: `glob("src/sol/**/*.sol")`
-   列出所有配置文件: `glob("src/lib/config/**/*.ts")`
