// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../contracts/ViraLogic.sol";
import "../contracts/ViraData.sol";

contract SimpleViraLogicTest is Test {
    ViraLogic viraLogic;
    
    function setUp() public {
        viraLogic = new ViraLogic();
    }
    
    function testBasicEncodeDecode() public {
        // Create a simple PumpReq with minimal data
        ViraData.PoolReq[] memory pairs = new ViraData.PoolReq[](1);
        pairs[0] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 2,
            fee: 300,
            fp: 100,
            inIndex: 0,
            outIndex: 1
        });
        
        ViraData.PumpReq memory pump = ViraData.PumpReq({
            convertEth: 1,
            calc: 2,
            gasLimit: 100000,
            cost: 50000000000000000, // 0.05 ETH
            amountIn: 1000000000000000000, // 1 ETH
            pairs: pairs
        });
        
        // Test with single PumpReq array
        ViraData.PumpReq[] memory pumps = new ViraData.PumpReq[](1);
        pumps[0] = pump;
        
        // Encode
        bytes memory encoded = viraLogic.encodePacked(pumps);
        
        // Decode
        ViraData.PumpReq[] memory decodedPumps = viraLogic.decodePackedOptimized(encoded);
        
        // Basic verification
        assertEq(decodedPumps.length, 1);
        assertEq(decodedPumps[0].convertEth, pump.convertEth);
        assertEq(decodedPumps[0].calc, pump.calc);
        assertEq(decodedPumps[0].gasLimit, pump.gasLimit);
        assertEq(decodedPumps[0].cost, pump.cost);
        assertEq(decodedPumps[0].amountIn, pump.amountIn);
        assertEq(decodedPumps[0].pairs.length, pump.pairs.length);
    }
}