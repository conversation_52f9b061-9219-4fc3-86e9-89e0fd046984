// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Test.sol";
import "../contracts/ViraLogic.sol";
import "../contracts/Vira.sol";
import "../contracts/IVira.sol";

contract BatchCheckPairFeeTestX is Test {
    ViraLogic public vira;
    address constant VIRA_ADDR = 0x08dE569680A18acF956d905F6997F98eedAd4805;
    address constant WOKB = 0xe538905cf8410324e03A5A23C1c177a474D59b2b;

    address constant TEST_PAIR = 0x721E5d8F4EEEBD5c2F2a7178E5dDae599654b95C; //usdt-WOKB

    address constant USDT_WOKB_LFG = 0x32d6B77ee026d471c191EA9EF9Ed404d2Cb4d4A6;
    address constant USDT_WOKB_PTO = 0xC71f9e1DE80Eb505C0cB3bBf90ae6593130e5D25;


    function setUp() public {
        // 部署合约
        vira = ViraLogic(payable(VIRA_ADDR));
        //vira = new ViraLogic();
        emit log_named_address("Vira Address", address(vira));
        emit log_named_address("Vira Owner Address", vira.owner());
        emit log_named_address("Vira Logic Address", vira.logicContact());

        // Deal WOKB tokens to the contract for testing
        deal(WOKB, address(vira), 100000 * 10**18);
        
        // Verify the balance
        uint256 balance = IERC20(WOKB).balanceOf(address(vira));
        emit log_named_uint("WOKB Balance", balance);
        require(balance >= 100000 * 10**18, "Insufficient WOKB balance");
    }

    function test_hello() public {
        emit log_named_string("Hello World", "Hello World");
    }

    function test_BatchCheckPairFeeX() public {
        // 设置调用者地址为operator
        vm.startPrank(vira.owner());

        // 构造测试数据

        //WOKB > USDT
    
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: TEST_PAIR,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 30,     // router fee
            inIndex: 1,
            outIndex: 0
        });

        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](0);

        //WOKB > usdr > WOKB
        /*
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: USDT_WOKB_LFG,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 30,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](1);
        prePair[0] = ViraData.PoolReq({
            addr: USDT_WOKB_PTO,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 25,     // router fee
            inIndex: 0,
            outIndex: 1
        });
        */


        //WOKB > feeToken
        /*
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: TEST_HOA_DAI,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](2);
        prePair[0] = ViraData.PoolReq({
            addr: TEST_PLSX_WOKB,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        prePair[1] = ViraData.PoolReq({
            addr: TEST_PLSX_DAI,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 0,
            outIndex: 1
        });
        */


        // 构造输入参数
        ViraLogic.CheckPairFeeInputDesc[] memory inputs = new ViraLogic.CheckPairFeeInputDesc[](1);
        inputs[0] = ViraLogic.CheckPairFeeInputDesc({
            pair: pair,
            prePair: prePair
        });

        // 调用合约函数
        address[] memory stables = new address[](1);
        stables[0] = WOKB;
        uint256[] memory stableAmountIns = new uint256[](1);
        stableAmountIns[0] = 100 * 10**18;
        uint256[][] memory fees = vira.batchCheckPairFee(inputs, stables, stableAmountIns);

        // 验证结果
        require(fees.length > 0, "Should return at least one result array");
        require(fees[0].length > 0, "Should return fee array");
        
        // 打印结果
        for (uint i = 0; i < fees[0].length; i++) {
            emit log_named_uint(
                string(abi.encodePacked("Token ", vm.toString(i), " Fee")), 
                fees[0][i]
            );
        }

        // 验证fee的范围
        for (uint i = 0; i < fees[0].length; i++) {
            uint256 fee = fees[0][i];
            if (fee != 666666) { // 如果不是honey token
                assertTrue(
                    fee <= 10000 || fee >= 900000,
                    "Fee should be <= 100000 (100%) or >= 900000 (error codes)"
                );
            }
        }

        vm.stopPrank();
    }

    function test_BatchCheckMevX() public {
        // 设置调用者地址为operator
        vm.startPrank(vira.owner());

        // 构造测试数据
        // WPLS > FIN > WPLS 路径
        ViraData.PoolReq[][] memory ds = new ViraData.PoolReq[][](1);
        ds[0] = new ViraData.PoolReq[](2);
        
        // WPLS-FIN pool
        ds[0][0] = ViraData.PoolReq({
            addr: USDT_WOKB_LFG,
            version: 2,    // UniswapV2 pool
            fee: 0,        // fee是需要检测的值
            fp: 30,      // router fee
            inIndex: 1,    // WPLS index
            outIndex: 0    // FIN index
        });

        // FIN-WPLS pool (完成循环)
        ds[0][1] = ViraData.PoolReq({
            addr: USDT_WOKB_PTO,
            version: 2,    // UniswapV2 pool
            fee: 0,        // fee是需要检测的值
            fp: 25,      // router fee
            inIndex: 0,    // FIN index
            outIndex: 1    // WPLS index
        });

        // 调用合约函数
        ViraLogic.CheckMevsResultDesc[] memory results = vira.batchCheckMev(ds, new address[](0), new uint[](0));

        //打印出results的原始结果
        emit log_named_uint("results length", results.length);
        emit log_named_uint("results[0].fee0 length", results[0].fee0.length);
        emit log_named_uint("results[0].fee1 length", results[0].fee1.length);
        emit log_array(results[0].fee0);
        emit log_array(results[0].fee1);
        emit log_named_uint("results[0].gas", results[0].gas);
    }


}