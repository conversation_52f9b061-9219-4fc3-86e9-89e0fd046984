// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Test.sol";
import "../contracts/ViraLogic.sol";
import "../contracts/Vira.sol";
import "../contracts/IVira.sol";

contract BatchCheckPairFeeTest is Test {
    ViraLogic public vira;
    address constant VIRA_ADDR = 0x63D3Da26B53A7066a221eD14b19070148059275a;
    //address constant VIRA_ADDR = 0x9B9c23aF94D1ea38317baED4e9e7e2E41e653FF8;
    //address constant VIRA_ADDR = 0x44D043bf722bcDAff7e04FbBDd9bD26C1bC6E018;
    address constant TEST_PAIR = 0xaE8429918FdBF9a5867e3243697637Dc56aa76A1; //dai-wpls
    address constant TEST_PAIR2 = 0x9B2EcC7ffDDF74a67600ee7E5a273561c626C99d; //dai-bear

    address constant TEST_FEE_PAIR = 0xA20397F5d6e27ca8e48Cc3d58137cD791d483077; //wpls-fee
    address constant WPLS = 0xA1077a294dDE1B09bB078844df40758a5D0f9a27;

    address constant TEST_HOA_DAI = 0xbD55D360D891cE4CCCaFB15C2aF85A42F2a63980; //hoa-dai
    address constant TEST_PLSX_DAI = 0xB2893ceA8080bF43b7b60B589EDaAb5211D98F23; //plsx-dai
    address constant TEST_PLSX_WPLS = 0x1b45b9148791d3a104184Cd5DFE5CE57193a3ee9; //plsx-wpls
    
    address constant TEST_PLS_USDT_LOW = 0x66725a01375Bb805ed5Ac65dEAd683019156e9c4; //usdt-wpls

    address constant FIN_WPLS_V2 = 0x615CfD552E98eB97e5557B03aa41D0E85e98167B; //fin-wpls
    address constant FIN_WPLS = 0x3474c4E0447E72BEf593f7A409E5Ae67A6cec9aD; //fin-wpls
    
    address constant FIN_AFFE = 0x18C2972dB0bD3958bCEC1311b3b548c2974E5dE6; //fin-affe
    address constant AFFE_WPLS = 0x155172653E94a7e5F0e04126803dcB6896796FBb; //affe-wpls

    function setUp() public {
        // 部署合约
        //vira = ViraLogic(payable(VIRA_ADDR));
        vira = new ViraLogic();
        emit log_named_address("Vira Address", address(vira));
        emit log_named_address("Vira Owner Address", vira.owner());
        emit log_named_address("Vira Logic Address", vira.logicContact());

        // Deal WPLS tokens to the contract for testing
        deal(WPLS, address(vira), 100000 * 10**18);
        
        // Verify the balance
        uint256 balance = IERC20(WPLS).balanceOf(address(vira));
        emit log_named_uint("WPLS Balance", balance);
        require(balance >= 100000 * 10**18, "Insufficient WPLS balance");
    }

    function test_hello() public {
        emit log_named_string("Hello World", "Hello World");
    }

    function test_BatchCheckPairFee() public {
        // 设置调用者地址为operator
        vm.startPrank(vira.owner());

        // 构造测试数据

        //wpls > fin
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: FIN_WPLS_V2,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](0);

        //wpls > dai > bear
        /*
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: TEST_PAIR2,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 0,
            outIndex: 1
        });
        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](1);
        prePair[0] = ViraData.PoolReq({
            addr: TEST_PAIR,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        */

        //wpls > feeToken
        /*
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: TEST_HOA_DAI,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        ViraData.PoolReq[] memory prePair = new ViraData.PoolReq[](2);
        prePair[0] = ViraData.PoolReq({
            addr: TEST_PLSX_WPLS,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 1,
            outIndex: 0
        });
        prePair[1] = ViraData.PoolReq({
            addr: TEST_PLSX_DAI,
            version: 2,    // UniswapV2 pool
            fee: 0,     // fee是需要检测的值
            fp: 9970,     // router fee
            inIndex: 0,
            outIndex: 1
        });
        */


        // 构造输入参数
        ViraLogic.CheckPairFeeInputDesc[] memory inputs = new ViraLogic.CheckPairFeeInputDesc[](1);
        inputs[0] = ViraLogic.CheckPairFeeInputDesc({
            pair: pair,
            prePair: prePair
        });

        // 调用合约函数
        address[] memory stables = new address[](1);
        stables[0] = WPLS;
        uint256[] memory stableAmountIns = new uint256[](1);
        stableAmountIns[0] = 100 * 10**18;
        uint256[][] memory fees = vira.batchCheckPairFee(inputs, stables, stableAmountIns);

        // 验证结果
        require(fees.length > 0, "Should return at least one result array");
        require(fees[0].length > 0, "Should return fee array");
        
        // 打印结果
        for (uint i = 0; i < fees[0].length; i++) {
            emit log_named_uint(
                string(abi.encodePacked("Token ", vm.toString(i), " Fee")), 
                fees[0][i]
            );
        }

        // 验证fee的范围
        for (uint i = 0; i < fees[0].length; i++) {
            uint256 fee = fees[0][i];
            if (fee != 666666) { // 如果不是honey token
                assertTrue(
                    fee <= 10000 || fee >= 900000,
                    "Fee should be <= 100000 (100%) or >= 900000 (error codes)"
                );
            }
        }

        vm.stopPrank();
    }

    // 测试错误情况
    function BatchCheckPairFeeWithInvalidPair() public {
        vm.startPrank(vira.owner());

        // 构造无效的pair地址
        ViraData.PoolReq[] memory pair = new ViraData.PoolReq[](1);
        pair[0] = ViraData.PoolReq({
            addr: address(0),  // 无效地址
            version: 2,
            fee: 9970,
            fp: 10000,
            inIndex: 0,
            outIndex: 1
        });

        ViraLogic.CheckPairFeeInputDesc[] memory inputs = new ViraLogic.CheckPairFeeInputDesc[](1);
        inputs[0] = ViraLogic.CheckPairFeeInputDesc({
            pair: pair,
            prePair: new ViraData.PoolReq[](0)
        });

        address[] memory stables = new address[](0);
        uint256[] memory stableAmountIns = new uint256[](0);
        uint256[][] memory fees = vira.batchCheckPairFee(inputs, stables, stableAmountIns);
        
        // 验证返回的错误码
        assertTrue(
            fees[0][0] >= 900000,
            "Should return error code for invalid pair"
        );

        vm.stopPrank();
    }


}