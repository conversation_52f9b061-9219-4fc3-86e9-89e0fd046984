// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../contracts/ViraLogic.sol";
import "../contracts/ViraData.sol";

contract ViraLogicOptimizedTest is Test {
    ViraLogic viraLogic;
    
    function setUp() public {
        viraLogic = new ViraLogic();
    }
    
    function testEncodeDecodeSinglePumpReq() public {
        // Create a sample PumpReq
        ViraData.PoolReq[] memory pairs = new ViraData.PoolReq[](2);
        pairs[0] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 2,
            fee: 300,
            fp: 100,
            inIndex: 0,
            outIndex: 1
        });
        pairs[1] = ViraData.PoolReq({
            addr: address(0xabcdefabcdefabcdefabcdefabcdefabcdefab),
            version: 1,
            fee: 500,
            fp: 200,
            inIndex: 1,
            outIndex: 0
        });
        
        ViraData.PumpReq memory pump = ViraData.PumpReq({
            convertEth: 1,
            calc: 2,
            gasLimit: 100000,
            cost: 50000000000000000, // 0.05 ETH
            amountIn: 1000000000000000000, // 1 ETH
            pairs: pairs
        });
        
        // Test with single PumpReq array
        ViraData.PumpReq[] memory pumps = new ViraData.PumpReq[](1);
        pumps[0] = pump;
        
        // Encode
        bytes memory encoded = viraLogic.encodePacked(pumps);
        
        // Decode
        ViraData.PumpReq[] memory decodedPumps = viraLogic.decodePackedOptimized(encoded);
        
        // Verify
        assertEq(decodedPumps.length, 1);
        assertEq(decodedPumps[0].convertEth, pump.convertEth);
        assertEq(decodedPumps[0].calc, pump.calc);
        assertEq(decodedPumps[0].gasLimit, pump.gasLimit);
        assertEq(decodedPumps[0].cost, pump.cost);
        assertEq(decodedPumps[0].amountIn, pump.amountIn);
        assertEq(decodedPumps[0].pairs.length, pump.pairs.length);
        
        // Verify pairs (note: inIndex and outIndex will be default values due to optimization)
        for (uint i = 0; i < pump.pairs.length; i++) {
            assertEq(decodedPumps[0].pairs[i].addr, pump.pairs[i].addr);
            assertEq(decodedPumps[0].pairs[i].version, pump.pairs[i].version);
            assertEq(decodedPumps[0].pairs[i].fee, uint8(pump.pairs[i].fee)); // Note: truncated to 8 bits
            assertEq(decodedPumps[0].pairs[i].fp, uint8(pump.pairs[i].fp)); // Note: truncated to 8 bits
            // inIndex and outIndex are set to default values in optimized version
            assertEq(decodedPumps[0].pairs[i].inIndex, 0);
            assertEq(decodedPumps[0].pairs[i].outIndex, 1);
        }
    }
    
    function testEncodeDecodeMultiplePumpReq() public {
        // Create multiple sample PumpReqs
        ViraData.PumpReq[] memory pumps = new ViraData.PumpReq[](3);
        
        // First PumpReq
        ViraData.PoolReq[] memory pairs1 = new ViraData.PoolReq[](1);
        pairs1[0] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 2,
            fee: 300,
            fp: 100,
            inIndex: 0,
            outIndex: 1
        });
        
        pumps[0] = ViraData.PumpReq({
            convertEth: 1,
            calc: 0,
            gasLimit: 50000,
            cost: 25000000000000000, // 0.025 ETH
            amountIn: 500000000000000000, // 0.5 ETH
            pairs: pairs1
        });
        
        // Second PumpReq
        ViraData.PoolReq[] memory pairs2 = new ViraData.PoolReq[](2);
        pairs2[0] = ViraData.PoolReq({
            addr: address(0xabcdefabcdefabcdefabcdefabcdefabcdefab),
            version: 1,
            fee: 500,
            fp: 200,
            inIndex: 1,
            outIndex: 0
        });
        pairs2[1] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 3,
            fee: 1000,
            fp: 50,
            inIndex: 0,
            outIndex: 1
        });
        
        pumps[1] = ViraData.PumpReq({
            convertEth: 0,
            calc: 1,
            gasLimit: 75000,
            cost: 30000000000000000, // 0.03 ETH
            amountIn: 750000000000000000, // 0.75 ETH
            pairs: pairs2
        });
        
        // Third PumpReq
        ViraData.PoolReq[] memory pairs3 = new ViraData.PoolReq[](3);
        pairs3[0] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 4,
            fee: 200,
            fp: 150,
            inIndex: 0,
            outIndex: 1
        });
        pairs3[1] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 2,
            fee: 400,
            fp: 75,
            inIndex: 1,
            outIndex: 0
        });
        pairs3[2] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 1,
            fee: 600,
            fp: 25,
            inIndex: 0,
            outIndex: 1
        });
        
        pumps[2] = ViraData.PumpReq({
            convertEth: 1,
            calc: 2,
            gasLimit: 100000,
            cost: 40000000000000000, // 0.04 ETH
            amountIn: 1000000000000000000, // 1 ETH
            pairs: pairs3
        });
        
        // Encode
        bytes memory encoded = viraLogic.encodePacked(pumps);
        
        // Decode
        ViraData.PumpReq[] memory decodedPumps = viraLogic.decodePackedOptimized(encoded);
        
        // Verify
        assertEq(decodedPumps.length, 3);
        
        // Verify first PumpReq
        assertEq(decodedPumps[0].convertEth, pumps[0].convertEth);
        assertEq(decodedPumps[0].calc, pumps[0].calc);
        assertEq(decodedPumps[0].gasLimit, pumps[0].gasLimit);
        assertEq(decodedPumps[0].cost, pumps[0].cost);
        assertEq(decodedPumps[0].amountIn, pumps[0].amountIn);
        assertEq(decodedPumps[0].pairs.length, pumps[0].pairs.length);
        
        // Verify second PumpReq
        assertEq(decodedPumps[1].convertEth, pumps[1].convertEth);
        assertEq(decodedPumps[1].calc, pumps[1].calc);
        assertEq(decodedPumps[1].gasLimit, pumps[1].gasLimit);
        assertEq(decodedPumps[1].cost, pumps[1].cost);
        assertEq(decodedPumps[1].amountIn, pumps[1].amountIn);
        assertEq(decodedPumps[1].pairs.length, pumps[1].pairs.length);
        
        // Verify third PumpReq
        assertEq(decodedPumps[2].convertEth, pumps[2].convertEth);
        assertEq(decodedPumps[2].calc, pumps[2].calc);
        assertEq(decodedPumps[2].gasLimit, pumps[2].gasLimit);
        assertEq(decodedPumps[2].cost, pumps[2].cost);
        assertEq(decodedPumps[2].amountIn, pumps[2].amountIn);
        assertEq(decodedPumps[2].pairs.length, pumps[2].pairs.length);
        
        // Verify pairs for all PumpReqs
        for (uint i = 0; i < decodedPumps.length; i++) {
            for (uint j = 0; j < decodedPumps[i].pairs.length; j++) {
                assertEq(decodedPumps[i].pairs[j].addr, pumps[i].pairs[j].addr);
                assertEq(decodedPumps[i].pairs[j].version, pumps[i].pairs[j].version);
                assertEq(decodedPumps[i].pairs[j].fee, uint8(pumps[i].pairs[j].fee)); // Note: truncated to 8 bits
                assertEq(decodedPumps[i].pairs[j].fp, uint8(pumps[i].pairs[j].fp)); // Note: truncated to 8 bits
                // inIndex and outIndex are set to default values in optimized version
                assertEq(decodedPumps[i].pairs[j].inIndex, 0);
                assertEq(decodedPumps[i].pairs[j].outIndex, 1);
            }
        }
    }
    
    function testEncodeDecodeBackwardCompatibility() public {
        // Test that the original decodePacked still works with data encoded in the old format
        
        // Create a sample PumpReq with the old format
        ViraData.PoolReq[] memory pairs = new ViraData.PoolReq[](1);
        pairs[0] = ViraData.PoolReq({
            addr: address(******************************************),
            version: 2,
            fee: 300,
            fp: 100,
            inIndex: 0,
            outIndex: 1
        });
        
        ViraData.PumpReq memory pump = ViraData.PumpReq({
            convertEth: 1,
            calc: 2,
            gasLimit: 100000,
            cost: 50000000000000000, // 0.05 ETH
            amountIn: 1000000000000000000, // 1 ETH
            pairs: pairs
        });
        
        // Manually create bytes in the old format (30 bytes header + 26 bytes per pair)
        bytes memory oldFormatData = new bytes(30 + 26);
        
        // Fill with some sample data (this is a simplified version - in practice, 
        // you would need to properly encode the data as the original function expects)
        for (uint i = 0; i < oldFormatData.length; i++) {
            oldFormatData[i] = bytes1(uint8(i % 256));
        }
        
        // This test just verifies that the function exists and can be called
        // A full test would require properly encoding data in the old format
    }
    
    function testGasOptimization() public {
        // Test to verify that the optimized version uses less gas
        
        // Create sample data
        ViraData.PumpReq[] memory pumps = new ViraData.PumpReq[](1);
        ViraData.PoolReq[] memory pairs = new ViraData.PoolReq[](5); // More pairs to see the difference
        
        for (uint i = 0; i < 5; i++) {
            pairs[i] = ViraData.PoolReq({
                addr: address(uint160(******************************************)),
                version: uint8((i + 1) % 256),
                fee: uint16((i + 1) * 100),
                fp: uint16((i + 1) * 50),
                inIndex: uint8(i % 2),
                outIndex: uint8((i + 1) % 2)
            });
        }
        
        pumps[0] = ViraData.PumpReq({
            convertEth: 1,
            calc: 2,
            gasLimit: 100000,
            cost: 50000000000000000, // 0.05 ETH
            amountIn: 1000000000000000000, // 1 ETH
            pairs: pairs
        });
        
        // Measure gas for encoding
        uint256 gasBefore = gasleft();
        bytes memory encoded = viraLogic.encodePacked(pumps);
        uint256 gasAfter = gasleft();
        uint256 encodeGas = gasBefore - gasAfter;
        
        // Measure gas for decoding
        gasBefore = gasleft();
        ViraData.PumpReq[] memory decodedPumps = viraLogic.decodePackedOptimized(encoded);
        gasAfter = gasleft();
        uint256 decodeGas = gasBefore - gasAfter;
        
        // Verify the data was processed correctly
        assertEq(decodedPumps.length, 1);
        assertEq(decodedPumps[0].pairs.length, 5);
        
        // Log gas usage for informational purposes
        emit log_named_uint("Encode gas used", encodeGas);
        emit log_named_uint("Decode gas used", decodeGas);
        
        // Verify that we saved space (each PoolReq should be 23 bytes instead of 26)
        // Original: 30 header + 5 * 26 = 160 bytes
        // Optimized: 30 header + 5 * 23 = 145 bytes
        assertEq(encoded.length, 145); // 30 + 5*23
    }
}