[profile.default]
src = "forge/contracts"
out = "forge/out"
test = "forge/test"
libs = ["forge/lib"]

#via_ir = true #stack too deep

optimizer = true
optimizer_runs = 200
# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options

#eth_rpc_url = "https://rpc-pulsechain.g4mm4.io"
#eth_rpc_url = "https://rpc.pulsechain.com"
#eth_rpc_url = "https://xlayerrpc.okx.com"
#eth_rpc_url = "https://endpoints.omniatech.io/v1/xlayer/mainnet/public"
eth_rpc_url = "https://rpc.ankr.com/xlayer"

evm_version = "shanghai" #for pulsechain


[profile.default.optimizer_details]
constantOptimizer = true
yul = false