{"dependencies": {"axios": "^1.11.0", "ethers": "^5.5.3", "hash-wasm": "^4.9.0", "node-fetch": "^3.2.0", "pm2": "^5.3.0", "precise": "^2.0.0"}, "name": "ether", "version": "1.0.0", "main": "checker.js", "devDependencies": {"@types/express": "^4.17.13", "@types/node": "^17.0.10", "@types/node-fetch": "^3.0.3", "@types/precise": "^0.0.28", "ts-node": "^10.9.2", "typescript": "^4.5.5"}, "scripts": {"start": "ts-node ./src/main.ts"}, "keywords": [], "author": "", "license": "ISC", "description": ""}